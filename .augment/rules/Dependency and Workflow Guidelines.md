---
type: "agent_requested"
description: "Project workflow standards, including dependency management with Poetry (`pyproject.toml`, `poetry.lock`) and Git best practices. Covers writing atomic, descriptive commits and enforcing CI checks via Pull Requests."
---
### 📦 Правила Зависимостей и Workflow

1.  **`poetry.lock` всегда в коммите.** Это гарантирует 100% воспроизводимость окружения для всех разработчиков и в CI.
2.  **Минимум зависимостей.** Перед `poetry add` подумай, действительно ли нужна новая библиотека. Разделяй зависимости на основные и для разработки (`--group dev`).
3.  **Атомарные коммиты.** Од<PERSON>н коммит — одно логическое изменение. Сообщение коммита объясняет **ЗАЧЕМ**, а не **ЧТО**. Используй префиксы: `Feat:`, `Fix:`, `Refactor:`, `Test:`.
4.  **PR проходит CI.** Ни один Pull Request не сливается в основную ветку, пока не пройдут быстрые тесты (`./scripts/test_fast.sh`).