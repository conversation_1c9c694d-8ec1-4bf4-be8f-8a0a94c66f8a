---
type: "agent_requested"
description: "Architectural guidelines for writing clean, maintainable, and performant Python code in the `src/` directory. Covers the fundamental requirement that all code changes must be accompanied by tests, single responsibility principle, avoiding magic numbers, using vectorized operations, and enforcing strict type hints."
---
### 🏛️ Правила Кода и Архитектуры (для `src/`)

1.  **Изменил код — изменил/добавил тест.** Любое изменение в коде (`src/`) **обязательно** сопровождается соответствующим изменением или добавлением теста. Новый код без тестов не принимается.

2.  **Одна функция — одна задача.** Избегай "божественных" функций. Разбивай сложную логику на мелкие, тестируемые части.

3.  **Никаких "магических чисел".** Все константы выноси в `src/optimiser/constants.py` или файлы конфигурации. В коде — только именованные переменные.

4.  **Производительность в приоритете.** Для операций с данными всегда используй векторизацию (`numpy`, `pandas`) или JIT-компиляцию (`numba`). Python-циклы по DataFrame'ам запрещены в критичных к скорости участках.

5.  **Строгая типизация обязательна.** Весь новый код должен иметь полные тайп-хинты и проходить проверку `mypy`.