---
type: "agent_requested"
description: "Core principles for writing high-quality pytest tests for this project. Focus on performance (fast/slow markers), isolation (unit/serial), determinism (seed), and preventing lookahead bias."
---
🏆 Итоговые правила написания тестов: Надежность без замедления
Наша цель: Добавлять надежные тесты, которые не замедляют разработку. Приоритет: unit > integration > e2e.
👑 Золотые правила (TL;DR)
Unit-тест — быстрый по умолчанию. Твой основной инструмент. Он проверяет одну вещь в изоляции. Требование: < 1 секунды.
Маркеры — обязательны. CI/CD-пайплайн полностью зависит от них.
@pytest.mark.slow: для тестов > 2-3 секунд (интеграция, бэктесты).
@pytest.mark.serial: для тестов, которые нельзя распараллеливать (глобальный кэш, SQLite).
Никакой случайности. Тесты на 100% детерминированы. Всегда фиксируй seed.
Никаких "взглядов в будущее". Логика для момента t использует данные только до t-1. Это главный закон проекта.
Один тест — одна проверка. Имя теста должно объяснять, что он делает (test_feature_when_condition_then_result).
🛠️ Практический чек-лист
1. Фикстуры и Данные
Малые данные: Используй готовые фикстуры: tiny_prices_df (для smoke/unit) или small_prices_df (для быстрых интеграционных тестов).
Файлы только в tmp_path: Никогда не читай/пиши в проектные директории. Тесты не должны оставлять "мусор".
Никаких sleep: Если нужно проверить асинхронность или время, мокируй time.sleep.
2. Мокирование и Изоляция
Мокируй всё тяжелое: @patch — твой лучший друг для изоляции от I/O, сети и сложных вычислений в unit-тестах.
Глобальное состояние — зло: Избегай тестов, меняющих глобальные переменные. Если это неизбежно — маркируй @pytest.mark.serial.
3. Параллельность (pytest -n auto)
Numba/BLAS: Если твой код использует многопоточность, ограничивай количество потоков через os.environ в тестах, чтобы избежать "перегрузки" CPU при параллельном запуске.
БД и кэши: Любой тест, который пишет в один и тот же файл (например, SQLite БД) или использует общий глобальный кэш, обязательно помечается @pytest.mark.serial.
4. Тесты Оптимизации (Optuna)
Минимализм: n_trials должен быть минимально необходимым для проверки логики (обычно 3-5).
Скорость: Используй RandomSampler и узкие диапазоны поиска.
Изоляция: Всегда используй tmp_path для SQLite storage и маркируй @pytest.mark.serial.
5. Стиль и Качество
Один инвариант: Один тест проверяет один поведенческий инвариант. Несколько сценариев — через @pytest.mark.parametrize.
Понятные ассерты: assert result == expected, f"Сообщение об ошибке с {контекстом}".
Численные сравнения: Для float используй np.isclose() или pytest.approx(). Для DataFrame — pd.testing.assert_frame_equal().
6. Чек-лист для Pull Request
Добавлены тесты: Новый код покрыт тестами (предпочтительно unit).
Все тесты проходят: Локально запущены ./scripts/test_fast.sh и ./scripts/test_full.sh.
Маркеры расставлены: Все новые медленные или последовательные тесты имеют маркеры slow и/или serial.

🚨 Красный тест — это сигнал, а не помеха. Если тест упал, не спеши ослаблять его условия (например, увеличивать допуски в pytest.approx или удалять assert). В первую очередь ищи и исправляй ошибку в коде. Ослабление теста — это крайняя мера, которая требует веского обоснования.
Нет дублирования: Логика не дублирует существующие тесты (использована параметризация).
Детерминизм: Все seed зафиксированы.