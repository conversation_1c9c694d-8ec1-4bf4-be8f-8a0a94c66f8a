/* Стили для веб-приложения анализа оптимизации */

:root {
    --text-primary: #000000;
    --text-secondary: #333333;
    --accent-color: #3498db;
    --card-bg: rgba(255, 255, 255, 0.95);
    --border-color: rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #000000;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 30px;
    text-align: center;
    position: relative;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
    opacity: 0.3;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    position: relative;
    z-index: 1;
}

.header .subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.main-content {
    padding: 30px;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.result-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-left: 5px solid;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.result-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.result-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.optimization-card {
    border-left-color: #2ecc71;
}

.validation-card {
    border-left-color: #e74c3c;
}

.degradation-card {
    border-left-color: #f39c12;
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.card-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.2rem;
    color: white;
}

.optimization-card .card-icon {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.validation-card .card-icon {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.degradation-card .card-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.card-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #000000;
}

.card-subtitle {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-style: italic;
    margin-top: 4px;
}

.metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #ecf0f1;
}

.metric:last-child {
    border-bottom: none;
}

.metric-label {
    font-weight: 500;
    color: #7f8c8d;
}

.metric-value {
    font-weight: 600;
    font-size: 1.1rem;
}

.metric-value.positive {
    color: #27ae60;
}

.metric-value.negative {
    color: #e74c3c;
}

.metric-value.neutral {
    color: #34495e;
}

.section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.section-title::before {
    content: '';
    width: 4px;
    height: 25px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    margin-right: 15px;
    border-radius: 2px;
}

.causes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.cause-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border-left: 4px solid #3498db;
    transition: all 0.3s ease;
}

.cause-item:hover {
    background: #e3f2fd;
    transform: translateX(5px);
}

.cause-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
}

.cause-description {
    color: #7f8c8d;
    font-size: 0.95rem;
    line-height: 1.5;
}

.parameters-comparison {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.param-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.param-name {
    font-weight: 500;
    color: #2c3e50;
}

.param-value {
    font-weight: 600;
    color: #3498db;
    font-family: 'Courier New', monospace;
}

.recommendations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

.recommendation-category {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 25px;
    border: 2px solid #dee2e6;
}

.category-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.category-title::before {
    content: '🎯';
    margin-right: 10px;
    font-size: 1.3rem;
}

.recommendation-item {
    background: white;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #3498db;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.recommendation-item:last-child {
    margin-bottom: 0;
}

.rec-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.rec-description {
    color: #7f8c8d;
    font-size: 0.95rem;
    line-height: 1.4;
}

.priority-high {
    border-left-color: #e74c3c;
}

.priority-medium {
    border-left-color: #f39c12;
}

.priority-low {
    border-left-color: #2ecc71;
}

.chart-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-top: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.chart-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
}

#degradationChart {
    max-width: 100%;
    height: 400px;
}

.status-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-critical {
    background: #fee;
    color: #c0392b;
    border: 1px solid #f8d7da;
}

.status-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 20px;
    margin-top: 40px;
}

.footer p {
    opacity: 0.8;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: 15px;
    }
    
    .header {
        padding: 20px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .section {
        padding: 20px;
    }
    
    .causes-grid,
    .recommendations-grid {
        grid-template-columns: 1fr;
    }
}

/* Анимации */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.result-card,
.section {
    animation: fadeInUp 0.6s ease-out;
}

.result-card:nth-child(2) {
    animation-delay: 0.1s;
}

.result-card:nth-child(3) {
    animation-delay: 0.2s;
}

/* Дополнительные утилиты */
.text-center {
    text-align: center;
}

.text-bold {
    font-weight: 600;
}

.text-muted {
    color: #7f8c8d;
}

.mb-20 {
    margin-bottom: 20px;
}

.mt-20 {
    margin-top: 20px;
}

/* Подобранные параметры */
.optimized-parameters {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 25px;
    border: 1px solid var(--border-color);
}

.parameters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.param-group {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.param-group-title {
    color: var(--accent-color);
    font-size: 1.1em;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--accent-color);
}

.param-group .param-item {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 10px;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.param-group .param-item:last-child {
    border-bottom: none;
}

.param-group .param-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95em;
}

.param-group .param-value {
    font-weight: 700;
    font-size: 1.1em;
    text-align: center;
}

.param-value.highlight {
    background: linear-gradient(135deg, var(--accent-color), #4CAF50);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.param-description {
    font-size: 0.85em;
    color: var(--text-secondary);
    text-align: right;
    font-style: italic;
}

.optimization-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    padding-top: 20px;
    border-top: 2px solid var(--border-color);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.summary-label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.9em;
}

.summary-value {
    font-weight: 700;
    font-size: 1.1em;
    color: var(--text-primary);
}

.summary-value.positive {
    color: #4CAF50;
}