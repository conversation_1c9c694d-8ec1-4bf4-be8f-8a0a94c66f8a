# Веб-анализ результатов оптимизации

Интерактивный веб-интерфейс для анализа результатов оптимизации стратегии парного трейдинга.

## 🔍 Проблемы и решения

### ❌ **Проблема 1**: Статичные данные
Изначально веб-анализ использовал **захардкоженные данные** в JavaScript файлах, которые не обновлялись автоматически при новых результатах оптимизации.

### ❌ **Проблема 2**: Смешанные данные
Данные оптимизации и валидации брались из одного источника, что создавало путаницу:
- Одинаковое количество сделок при разных Sharpe ratio
- Периоды отображались как числа (2.0000 вместо текста)
- Нет разделения между результатами Optuna и walk-forward теста

### ❌ **Проблема 3**: Некорректное форматирование
Функция анимации счетчиков обрабатывала ВСЕ значения как числа, включая строки.

### ✅ **Решения**:

#### **1. Автоматическое обновление**
Создан скрипт `scripts/update_web_analysis.py`, который:
1. Читает результаты из Optuna БД
2. Загружает метрики валидации из CSV файлов
3. Обновляет JavaScript файлы с новыми данными
4. Автоматически запускается после каждой оптимизации

#### **2. Разделение источников данных**
- **Оптимизация**: Только Sharpe ratio из Optuna (детали = "N/A")
- **Валидация**: Полные результаты walk-forward теста
- **Четкие подписи**: "Лучший Sharpe из Optuna" vs "Walk-forward тестирование"

#### **3. Исправление форматирования**
- Добавлен класс `no-animate` для строковых значений
- Функция анимации игнорирует текстовые поля
- Периоды отображаются как текст, а не числа

## 📁 Структура файлов

```
src/web_analysis/
├── index.html          # Главная HTML страница
├── styles.css          # CSS стили
├── analysis.js         # Данные и логика анализа (обновляется автоматически)
├── app.js             # Интерактивность и визуализация
└── README.md          # Эта документация
```

## 🚀 Использование

### Автоматическое обновление (рекомендуется)
```bash
# Полный цикл: оптимизация + обновление веб-анализа
python scripts/run_optimization_with_web_update.py \
    --n-trials 50 \
    --study-name my_optimization

# Результат: веб-анализ автоматически обновится
```

### Ручное обновление
```bash
# Только обновление веб-анализа (если оптимизация уже выполнена)
python scripts/update_web_analysis.py \
    study_name \
    outputs/studies/study_name.db \
    results
```

### Просмотр результатов
1. Откройте: `file:///path/to/project/src/web_analysis/index.html`
2. Обновите страницу в браузере (Ctrl+F5)
3. Изучите интерактивные графики и метрики

## 📊 Отображаемые данные

### Результаты оптимизации
- **Sharpe ratio** - из лучшего trial Optuna
- **Количество сделок** - из результатов валидации
- **Период** - информация о количестве trials
- **Параметры** - лучшие найденные значения

### Результаты валидации  
- **Sharpe ratio** - из файла strategy_metrics.csv
- **Количество сделок** - общее количество сделок
- **P&L** - итоговая прибыль/убыток
- **Период** - период валидации

### Анализ деградации
- **Сравнение** оптимизации vs валидации
- **Процент деградации** показателей
- **Диагностика переоптимизации**
- **Рекомендации** по улучшению

## 🔧 Техническая информация

### Источники данных
1. **Optuna БД** (`outputs/studies/*.db`) - результаты оптимизации
2. **CSV файлы** (`results/strategy_metrics.csv`) - метрики валидации
3. **Конфигурации** (`configs/best_config.yaml`) - лучшие параметры

### Обновление данных
Скрипт `update_web_analysis.py` выполняет точечную замену значений в `analysis.js`:
- Ищет конкретные строки с данными
- Заменяет их на актуальные значения
- Сохраняет структуру и комментарии JavaScript

### Совместимость
- Работает в любом современном браузере
- Не требует веб-сервера (файловый протокол)
- Использует Chart.js для интерактивных графиков

## 🎯 Примеры команд

### Быстрая оптимизация с веб-обновлением
```bash
python scripts/run_optimization_with_web_update.py \
    --n-trials 20 \
    --study-name quick_test \
    --search-space configs/search_space_relaxed.yaml
```

### Полная оптимизация с веб-обновлением
```bash
python scripts/run_optimization_with_web_update.py \
    --n-trials 100 \
    --study-name full_optimization \
    --search-space configs/search_space.yaml
```

### Только обновление веб-анализа
```bash
python scripts/update_web_analysis.py \
    pairs_strategy_v1 \
    outputs/studies/pairs_strategy_v1.db
```

## 🔄 Автоматизация

### Интеграция в процесс оптимизации
Используйте `run_optimization_with_web_update.py` вместо обычного `run_optimization.py` для автоматического обновления веб-анализа.

### Мониторинг результатов
1. Запустите оптимизацию с веб-обновлением
2. Откройте веб-страницу в браузере
3. Периодически обновляйте страницу для просмотра прогресса
4. Анализируйте результаты в интерактивном интерфейсе

## 🎨 Возможности веб-интерфейса

- **Интерактивные карточки** с ключевыми метриками
- **Графики деградации** показателей
- **Сравнение параметров** оптимизации и валидации
- **Диагностика переоптимизации** с рекомендациями
- **Анимированные счетчики** для лучшего восприятия
- **Экспорт отчетов** в JSON формате

---

**Теперь веб-анализ всегда показывает актуальные данные!** 🎉
