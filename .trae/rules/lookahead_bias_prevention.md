# Правила предотвращения Lookahead Bias

## Критически важно: НИКОГДА не создавать lookahead bias!

### Что такое lookahead bias?
Lookahead bias (опережающее смещение) - это использование информации, которая не была бы доступна в момент принятия торгового решения в реальном времени.

### Основные правила:

1. **Расчет статистик**: При расчете rolling статистик (beta, mean, std) на баре i использовать только данные до бара i-1 включительно

2. **Применение статистик**: Статистики, рассчитанные на баре i, применять только к предыдущему бару (i-1) или более поздним барам

3. **Генерация сигналов**: Сигналы генерировать только на основе данных, доступных до текущего момента

4. **Проверка в коде**: 
   - В `update_rolling_stats()`: использовать `df["y"].iat[i-1]` для расчета spread
   - В `compute_signal()`: использовать данные с задержкой минимум на 1 бар
   - Никогда не использовать `df["y"].iat[i]` для расчета статистик, которые будут применены к тому же бару i

5. **Тестирование**: Всегда проверять, что изменение будущих данных не влияет на прошлые сигналы

### Примеры нарушений:
- ❌ `curr_spread = df["y"].iat[i] - beta * df["x"].iat[i]` (использует данные текущего бара)
- ✅ `prev_spread = df["y"].iat[i-1] - beta * df["x"].iat[i-1]` (использует данные предыдущего бара)

### Помни:
Lookahead bias делает бэктест нереалистично оптимистичным и приводит к провалу стратегии в реальной торговле!