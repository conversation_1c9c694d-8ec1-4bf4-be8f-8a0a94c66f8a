Добавляй тесты, не просто формальные, а чтобы реально и строго проверяли корректную работу бэктеста и исправление этих ошибок.
Тесты должны иметь понятное название того что они проверяют. Тесты должны содержать описание того что проверяется. Тесты должны быть строгими к результатам, нельзя увеличивать погрешность, нельзя ослаблять тесты чтобы они проходили. Не перезатирай существующие тесты.
Имей в виду: данные 15-минутные. Основной конфигурационный файл: main_2024.
Не плоди лишние файлы, всегда старайся изменять основной файл с кодом или конфигурацией вместо создания нового.
Не создавай .md отчет о результатах исправлений.
При исправлениях не допускать возникновения lookahead bias - статистики должны рассчитываться только на основе исторических данных.

## Правила для определения завершения pytest

### Запуск тестов
Запускай тесты командой:
```
pytest -q --disable-warnings --junitxml=report.xml
```

### Критерий завершения
Считай прогон завершённым, когда:
- Процесс pytest завершился (получен код выхода), или
- В stdout встретилась строка вида PYTEST_SESSION_FINISHED_EXIT_STATUS=

### Маркер в конце сессии
В проект добавлен хук в conftest.py:
```python
def pytest_sessionfinish(session, exitstatus):
    print(f"PYTEST_SESSION_FINISHED_EXIT_STATUS={exitstatus}")
```
Агент обязан искать эту строку. Число после = — код выхода.

### Интерпретация кода выхода
- 0 → все тесты прошли успешно
- Любое другое значение (1, 2 и т.д.) → прогон завершён с ошибками/падениями, но это всё равно завершение, нужно разобрать отчёт

### Сбор результатов
После завершения:
- Прочитай файл report.xml (JUnit) и извлеки количество tests, failures, errors, skipped
- Если есть failures/errors, выведи список упавших тестов с сообщениями AssertionError (парсить <testcase> с <failure>)

### Отчёт агента
В ответе пользователю всегда указывай:
- Итог: Passed / Failed (кол-во)
- Перечень упавших тестов и короткое сообщение причины
- Предложение следующего шага (например: "Запусти pytest tests/...::конкретный_тест -vv для детализации")

### Игнорирование предупреждений
Предупреждения (FutureWarning, DeprecationWarning) не влияют на критерий завершения. Не трактуй их как причину «ожидания».