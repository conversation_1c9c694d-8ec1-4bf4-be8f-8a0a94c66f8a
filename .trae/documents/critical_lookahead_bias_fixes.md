# Исправление критических ошибок Lookahead Bias в системе парного трейдинга

## 1. Обзор продукта

Система оптимизации стратегии парного трейдинга содержит 4 критические проблемы lookahead bias и логических ошибок, которые приводят к нереалистично завышенным результатам бэктестинга. Эти ошибки делают результаты оптимизации недостоверными и могут привести к значительным потерям в реальной торговле.

Основная цель: устранить все источники lookahead bias и логические ошибки для получения реалистичных результатов бэктестинга.

## 2. Основные функции

### 2.1 Модули исправлений

Наши исправления состоят из следующих основных компонентов:

1. **Исправление отбора пар**: перенос логики отбора пар в цикл walk-forward
2. **Исправление исполнения сигналов**: обеспечение задержки сигнала на 1 бар
3. **Исправление заполнения пропусков**: удаление .bfill() методов
4. **Исправление подсчета метрик**: корректный расчет сделок и торговых дней

### 2.2 Детали исправлений

| Компонент            | Модуль                                          | Описание функции                                                                                             |
| -------------------- | ----------------------------------------------- | ------------------------------------------------------------------------------------------------------------ |
| Отбор пар            | FastWalkForwardObjective                        | Перенести логику из preselect\_pairs.py в \_run\_fast\_backtest, выполнять отбор на каждом walk-forward шаге |
| Исполнение сигналов  | base\_engine.py                                 | Изменить порядок операций: исполнить сигнал с предыдущего бара, затем сгенерировать новый                    |
| Заполнение пропусков | normalization\_improvements.py, data\_loader.py | Удалить все вызовы .bfill(), оставить только ffill и interpolate                                             |
| Подсчет метрик       | FastWalkForwardObjective                        | Исправить расчет win\_rate, trading\_days и trades\_per\_day                                                 |

## 3. Основной процесс

### Процесс исправления ошибок:

1. **Анализ текущего состояния**: выявление всех источников lookahead bias
2. **Поэтапное исправление**: устранение каждой проблемы с добавлением тестов
3. **Валидация**: проверка корректности исправлений через строгие тесты
4. **Интеграция**: обеспечение совместимости всех исправлений

```mermaid
graph TD
    A[Анализ проблем] --> B[Исправление отбора пар]
    B --> C[Исправление сигналов]
    C --> D[Исправление заполнения]
    D --> E[Исправление метрик]
    E --> F[Валидация тестами]
    F --> G[Интеграция изменений]
```

## 4. Дизайн интерфейса

### 4.1 Стиль дизайна

* **Основные цвета**: Красный (#FF4444) для критических ошибок, зеленый (#44AA44) для исправлений

* **Стиль кнопок**: Плоские кнопки с четкими границами

* **Шрифт**: Monospace для кода, Sans-serif для текста

* **Стиль макета**: Структурированный список с четкой иерархией

* **Иконки**: Предупреждающие символы ⚠️ для проблем, галочки ✅ для решений

### 4.2 Обзор дизайна страниц

| Компонент            | Модуль               | UI элементы                                                      |
| -------------------- | -------------------- | ---------------------------------------------------------------- |
| Документация проблем | Markdown             | Структурированные списки, блоки кода, диаграммы Mermaid          |
| Планы исправлений    | Пошаговые инструкции | Нумерованные списки, выделение критических моментов              |
| Тесты                | Python код           | Четко структурированные тестовые функции с описательными именами |

### 4.3 Адаптивность

Документ оптимизирован для чтения в IDE и текстовых редакторах, с четкой структурой и минимальным использованием специального форматирования.

## 5. Детальные планы исправлений

### 5.1 Проблема 1: Lookahead Bias в отборе пар

**Критическая проблема**: FastWalkForwardObjective использует один предварительно отобранный набор пар для всех walk-forward шагов, что создает lookahead bias.

**План исправления**:

1. Перенести логику из `scripts/preselect_pairs.py` в метод `_run_fast_backtest`
2. На каждом walk-forward шаге выполнять отбор пар на тренировочных данных
3. Удалить файл `scripts/preselect_pairs.py`
4. Удалить загрузку `preselected_pairs.csv` из `__init__`

**Новый тест**: `tests/test_multiple_walk_forward_steps.py`

* Функция: `test_pair_selection_is_done_per_step`

* Проверка: мок функции отбора пар вызывается для каждого шага

### 5.2 Проблема 2: Lookahead Bias при исполнении сигналов

**Критическая проблема**: Сигнал, сгенерированный на баре i, исполняется на том же баре i, что невозможно в реальности.

**План исправления**:

1. В `base_engine.py` изменить порядок операций в `_run_single_backtest_internal`
2. На итерации i: сначала исполнить сигнал с i-1, затем сгенерировать новый сигнал
3. Унифицировать с логикой в `numba_kernels.py`

**Новый тест**: `tests/test_lookahead_bias_fix.py`

* Функция: `test_signal_lag_is_enforced`

* Проверка: изменение позиции происходит на k+1, а не на k

### 5.3 Проблема 3: Lookahead Bias при заполнении пропусков

**Критическая проблема**: Использование `.bfill()` заполняет пропуски данными из будущего.

**План исправления**:

1. Найти все вызовы `.bfill()` в файлах нормализации
2. Удалить их, оставить только `ffill` и `interpolate`
3. Обновить функцию `_fill_gaps_session_aware`

**Новый тест**: `tests/test_data_processing_fixes.py`

* Функция: `test_bfill_is_not_used_across_sessions`

* Проверка: NaN в начале дня не заполняется данными предыдущего дня

### 5.4 Проблема 4: Неверный подсчет сделок и торговых дней

**Критическая проблема**: Метрики рассчитываются неверно, что влияет на работу Optuna.

**План исправления**:

1. Исправить подсчет `total_trades` как реальных сделок (вход/выход)
2. Изменить расчет `win_rate` и `trading_days`
3. Обновить формулу `trades_per_day`

**Новый тест**: `tests/test_critical_trade_counting_fixes.py`

* Функция: `test_win_rate_and_trade_days_are_correct`

* Проверка: корректность расчета win\_rate и trading\_days

## 6. Критерии успеха

* ✅ Все новые тесты проходят успешно

* ✅ Отсутствие lookahead bias во всех компонентах

* ✅ Реалистичные результаты бэктестинга

* ✅ Корректная работа оптимизации Optuna

* ✅ Совместимость с существующей кодовой базой

## 7. Файлы для изменения

**Основные файлы**:

* `src/optimiser/fast_objective.py` - перенос логики отбора пар

* `src/coint2/engine/base_engine.py` - исправление исполнения сигналов

* `src/coint2/core/normalization_improvements.py` - удаление bfill

* `src/coint2/core/data_loader.py` - удаление bfill

**Файлы для удаления**:

* `scripts/preselect_pairs.py` - логика перенесена в основной процесс

**Новые тестовые файлы**:

* `tests/test_multiple_walk_forward_steps.py`

* `tests/test_lookahead_bias_fix.py`

* `tests/test_data_processing_fixes.py`

* `tests/test_critical_trade_counting_fixes.py`

