# Ультра-быстрое пространство поиска для оптимизации
# Сужены диапазоны и зафиксированы менее важные параметры

signals:
  zscore_threshold:
    low: 0.7
    high: 1.1
  zscore_exit:
    low: 0.1
    high: 0.3
  hysteresis:
    low: 0.4
    high: 0.6
  rolling_window:
    low: 15
    high: 30
    step: 5

portfolio:
  max_active_positions:
    low: 10
    high: 20
    step: 5
  risk_per_position_pct:
    low: 0.01
    high: 0.05
  max_position_size_pct:
    low: 0.08
    high: 0.15

costs:
  # Фиксированные значения для ускорения
  commission_pct: 0.0004
  slippage_pct: 0.0005

normalization:
  # Фиксируем лучший метод
  normalization_method:
    - minmax
  min_history_ratio:
    low: 0.5
    high: 0.7

risk_management:
  stop_loss_multiplier:
    low: 3.0
    high: 6.0
  time_stop_multiplier:
    low: 5.0
    high: 10.0
  cooldown_hours:
    low: 1
    high: 4
    step: 1

# Настройки анти-чурн штрафа
metrics:
  anti_churn_penalty: 0.02
  max_trades_per_day: 5
