# Минимальная конфигурация для быстрых тестов
# Оптимизирована для скорости выполнения

data_dir: "data_downloaded"
results_dir: "results"

# Упрощенная предобработка данных
data_processing:
  normalization_method: "minmax"
  fill_method: "linear"
  min_history_ratio: 0.6  # Снижено для быстрых тестов
  handle_constant: true

# Минимальные настройки портфеля
portfolio:
  initial_capital: 10000.0
  risk_per_position_pct: 0.01
  max_active_positions: 3  # Уменьшено для быстрых тестов
  max_margin_usage: 0.5
  volatility_based_sizing: false  # Отключено для скорости
  min_position_size_pct: 0.005
  max_position_size_pct: 0.02

# Упрощенный отбор пар
pair_selection:
  lookback_days: 30  # Уменьшено
  coint_pvalue_threshold: 0.2  # Ослаблено
  ssd_top_n: 1000  # Сильно уменьшено
  min_half_life_days: 1
  max_half_life_days: 10
  min_mean_crossings: 1
  adaptive_quantiles: false

# Минимальные настройки бэктеста
backtest:
  zscore_threshold: 2.0
  zscore_exit: 0.5
  rolling_window: 20  # Минимальное окно
  commission_pct: 0.001
  slippage_pct: 0.001
  bid_ask_spread_pct: 0.001
  annualizing_factor: 252
  capital_at_risk: 1.0
  stop_loss_multiplier: 2.0
  cooldown_periods: 0
  wait_for_candle_close: false
  max_margin_usage: 0.5

# Упрощенные настройки walk-forward
walk_forward:
  start_date: "2023-08-01"
  end_date: "2023-08-15"  # Короткий период для тестов
  training_period_days: 7  # Минимальный период
  testing_period_days: 3   # Минимальный период
  max_shards: 1  # Без шардинга

# Минимальные настройки оптимизации
optimization:
  n_trials: 5  # Очень мало для быстрых тестов
  timeout_seconds: 60
  n_jobs: 1  # Без параллелизации
  sampler: "random"
  pruner: "median"
  
  # Упрощенные диапазоны параметров
  param_ranges:
    zscore_threshold: [1.5, 2.5]
    zscore_exit: [0.2, 0.8]
    rolling_window: [15, 25]

# Отключаем сложные фичи для скорости
advanced_features:
  market_regime_detection: false
  volatility_based_sizing: false
  adaptive_thresholds: false
  enhanced_risk_management: false
