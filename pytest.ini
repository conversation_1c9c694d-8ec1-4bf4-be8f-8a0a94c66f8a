[pytest]
markers =
    slow: долгие интеграционные/оптимизационные тесты (>30 сек)
    serial: нельзя параллелить (например, SQLite/глобальные кэши)
    smoke: критически важные быстрые тесты (<5 сек общее время)
    fast: быстрые тесты для CI/CD (<30 сек общее время)
    integration: интеграционные тесты с реальными данными
    performance: тесты производительности и бенчмарки
    memory: тесты использования памяти
    cache: тесты кэширования
    concurrent: тесты конкурентности
    unit: изолированные юнит-тесты с моками
    critical_fixes: тесты критических исправлений
    deprecated: устаревшие тесты для удаления

addopts = -v --tb=short --durations=10 --strict-markers -m "not slow and not serial"

testpaths = tests

python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Фильтры для предупреждений
filterwarnings =
    # Скрываем шум от сторонних либ; своё чиним
    ignore::DeprecationWarning:optuna\..*
    ignore::DeprecationWarning:pandas\..*
    ignore::DeprecationWarning:numpy\..*
    ignore::PendingDeprecationWarning:optuna\..*
    ignore::PendingDeprecationWarning:pandas\..*
    ignore::PendingDeprecationWarning:numpy\..*
    # Строгий режим для собственного кода (будет включен после исправления numpy алиасов)
    # error::DeprecationWarning:src\..*
