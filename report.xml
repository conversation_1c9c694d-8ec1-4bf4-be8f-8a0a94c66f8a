<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="0" skipped="8" tests="242" time="137.413" timestamp="2025-08-02T15:02:39.650990" hostname="MacBook-Pro.local"><testcase classname="tests.test_03_backtest_engine_optimized.TestOptimizedPairBacktester" name="test_optimized_backtest_without_cache" time="0.676" /><testcase classname="tests.test_03_backtest_engine_optimized.TestOptimizedPairBacktester" name="test_optimized_backtest_with_cache_initialization" time="0.552" /><testcase classname="tests.test_03_backtest_engine_optimized.TestOptimizedPairBacktester" name="test_backtest_results_consistency_with_cache_vs_without" time="1.087" /><testcase classname="tests.test_03_backtest_engine_optimized.TestOptimizedPairBacktester" name="test_cached_rolling_statistics_accuracy" time="0.552" /><testcase classname="tests.test_03_backtest_engine_optimized.TestOptimizedPairBacktester" name="test_optimized_backtest_performance_characteristics" time="1.121" /><testcase classname="tests.test_03_backtest_engine_optimized.TestOptimizedPairBacktester" name="test_symbol_name_extraction_and_setting" time="0.003" /><testcase classname="tests.test_03_backtest_engine_optimized.TestOptimizedPairBacktester" name="test_edge_case_empty_pair_data" time="0.004" /><testcase classname="tests.test_03_backtest_engine_optimized.TestOptimizedPairBacktester" name="test_edge_case_insufficient_data_for_rolling_window" time="0.066" /><testcase classname="tests.test_03_backtest_engine_optimized.TestOptimizedPairBacktester" name="test_trading_logic_consistency" time="0.607" /><testcase classname="tests.test_03_backtest_engine_optimized.TestOptimizedPairBacktester" name="test_pnl_calculation_reasonableness" time="0.546" /><testcase classname="tests.test_03_backtest_engine_optimized.TestOptimizedPairBacktester" name="test_cache_miss_handling" time="0.547" /><testcase classname="tests.test_03_backtest_engine_optimized.TestOptimizedPairBacktester" name="test_data_alignment_with_different_lengths" time="0.546" /><testcase classname="tests.test_03_backtest_engine_optimized.TestOptimizedPairBacktester" name="test_inheritance_from_pair_backtester" time="0.002" /><testcase classname="tests.test_10_integration_pipeline.TestBacktestIntegration" name="test_full_backtest_pipeline_consistency" time="2.034" /><testcase classname="tests.test_10_integration_pipeline.TestBacktestIntegration" name="test_position_entry_exit_logic" time="1.933" /><testcase classname="tests.test_10_integration_pipeline.TestBacktestIntegration" name="test_trading_costs_consistency" time="3.857" /><testcase classname="tests.test_10_integration_pipeline.TestBacktestIntegration" name="test_risk_management_limits" time="1.976" /><testcase classname="tests.test_10_integration_pipeline.TestBacktestIntegration" name="test_edge_cases_handling" time="0.463" /><testcase classname="tests.test_10_integration_pipeline.TestBacktestIntegration" name="test_performance_metrics_calculation" time="1.873" /><testcase classname="tests.test_10_integration_pipeline.TestBacktestIntegration" name="test_incremental_vs_batch_consistency" time="3.940" /><testcase classname="tests.test_11_robustness_checks.TestBacktestRobustness" name="test_synthetic_random_walk" time="4.538" /><testcase classname="tests.test_11_robustness_checks.TestBacktestRobustness" name="test_position_limits_respected" time="4.826" /><testcase classname="tests.test_11_robustness_checks.TestBacktestRobustness" name="test_equity_curve_monotonicity" time="4.716" /><testcase classname="tests.test_15min_critical_fixes.TestCapitalControlWithPositionLimit" name="test_position_scaling_when_exceeding_limit" time="0.009" /><testcase classname="tests.test_15min_critical_fixes.TestSignalLagFix" name="test_signal_lag_prevents_lookahead_bias" time="0.078" /><testcase classname="tests.test_15min_critical_fixes.TestSessionAwareDataFilling" name="test_no_overnight_forward_fill" time="0.045" /><testcase classname="tests.test_15min_critical_fixes" name="test_comprehensive_15min_fixes" time="0.020" /><testcase classname="tests.test_22_synthetic_scenarios.TestSyntheticScenarios" name="test_perfect_mean_reversion" time="2.192" /><testcase classname="tests.test_22_synthetic_scenarios.TestSyntheticScenarios" name="test_trending_market_performance" time="1.182" /><testcase classname="tests.test_22_synthetic_scenarios.TestSyntheticScenarios" name="test_high_volatility_scenario" time="1.656" /><testcase classname="tests.test_22_synthetic_scenarios.TestSyntheticScenarios" name="test_cointegration_breakdown" time="2.724" /><testcase classname="tests.test_22_synthetic_scenarios.TestSyntheticScenarios" name="test_extreme_price_movements" time="0.913" /><testcase classname="tests.test_22_synthetic_scenarios.TestSyntheticScenarios" name="test_zero_volatility_period" time="0.235" /><testcase classname="tests.test_22_synthetic_scenarios.TestSyntheticScenarios" name="test_missing_data_handling" time="0.417" /><testcase classname="tests.test_22_synthetic_scenarios.TestSyntheticScenarios" name="test_single_asset_constant" time="0.102" /><testcase classname="tests.test_30_critical_fixes_main.TestCriticalFixesVerification" name="test_entry_beta_storage_and_usage" time="0.793" /><testcase classname="tests.test_30_critical_fixes_main.TestCriticalFixesVerification" name="test_no_double_cost_accounting" time="0.761" /><testcase classname="tests.test_30_critical_fixes_main.TestCriticalFixesVerification" name="test_cash_management_consistency" time="0.756" /><testcase classname="tests.test_30_critical_fixes_main.TestCriticalFixesVerification" name="test_unrealized_pnl_with_entry_beta" time="0.783" /><testcase classname="tests.test_30_critical_fixes_main.TestCriticalFixesVerification" name="test_step_pnl_calculation_correctness" time="0.772" /><testcase classname="tests.test_30_critical_fixes_main.TestCriticalFixesVerification" name="test_cumulative_pnl_consistency" time="0.769" /><testcase classname="tests.test_30_critical_fixes_main.TestCriticalFixesVerification" name="test_beta_consistency_during_position" time="0.763" /><testcase classname="tests.test_30_critical_fixes_main.TestCriticalFixesVerification" name="test_no_lookahead_bias_in_beta_usage" time="0.763" /><testcase classname="tests.test_32_numba_fixes_verification.TestNumbaFixesVerification" name="test_entry_beta_consistency" time="0.000"><skipped type="pytest.skip" message="Numba function signature changed, needs update">/Users/<USER>/Desktop/coint4/tests/test_32_numba_fixes_verification.py:12: Numba function signature changed, needs update</skipped></testcase><testcase classname="tests.test_32_numba_fixes_verification.TestNumbaFixesVerification" name="test_no_lookahead_bias_in_entry_prices" time="0.000"><skipped type="pytest.skip" message="Numba function signature changed, needs update">/Users/<USER>/Desktop/coint4/tests/test_32_numba_fixes_verification.py:51: Numba function signature changed, needs update</skipped></testcase><testcase classname="tests.test_32_numba_fixes_verification.TestNumbaFixesVerification" name="test_nan_handling_in_signals" time="0.000"><skipped type="pytest.skip" message="Numba function signature changed, needs update">/Users/<USER>/Desktop/coint4/tests/test_32_numba_fixes_verification.py:87: Numba function signature changed, needs update</skipped></testcase><testcase classname="tests.test_32_numba_fixes_verification.TestNumbaFixesVerification" name="test_rolling_ols_no_lookahead" time="0.000"><skipped type="pytest.skip" message="Numba function signature changed, needs update">/Users/<USER>/Desktop/coint4/tests/test_32_numba_fixes_verification.py:120: Numba function signature changed, needs update</skipped></testcase><testcase classname="tests.test_32_numba_fixes_verification.TestNumbaFixesVerification" name="test_position_consistency_across_regime_changes" time="0.000"><skipped type="pytest.skip" message="Numba function signature changed, needs update">/Users/<USER>/Desktop/coint4/tests/test_32_numba_fixes_verification.py:156: Numba function signature changed, needs update</skipped></testcase><testcase classname="tests.test_33_critical_fixes_integration.TestCriticalFixesIntegration" name="test_base_engine_critical_fixes" time="0.751" /><testcase classname="tests.test_33_critical_fixes_integration.TestCriticalFixesIntegration" name="test_numba_engine_critical_fixes" time="0.013" /><testcase classname="tests.test_33_critical_fixes_integration.TestCriticalFixesIntegration" name="test_capital_management_fixes" time="0.002" /><testcase classname="tests.test_33_critical_fixes_integration.TestCriticalFixesIntegration" name="test_trading_costs_improvements" time="0.002" /><testcase classname="tests.test_backtest_correctness_with_blas.TestBacktestCorrectnessWithBLAS" name="test_process_single_pair_consistency" time="0.073" /><testcase classname="tests.test_backtest_correctness_with_blas.TestBacktestCorrectnessWithBLAS" name="test_numerical_stability_with_blas_limits" time="5.586" /><testcase classname="tests.test_critical_fixes.TestCriticalFixes" name="test_import_fix_backtest_optimizer" time="0.000" /><testcase classname="tests.test_critical_fixes.TestCriticalFixes" name="test_pruning_optimization" time="0.000" /><testcase classname="tests.test_critical_fixes.TestCriticalFixes" name="test_log_scale_fix" time="0.005" /><testcase classname="tests.test_critical_fixes.TestCriticalFixes" name="test_exception_handling_improvement" time="2.759" /><testcase classname="tests.test_critical_logical_fixes.TestCriticalFixes" name="test_no_duplicate_optuna_import" time="0.002" /><testcase classname="tests.test_critical_logical_fixes.TestCriticalFixes" name="test_pairs_skipped_variables_defined" time="0.001" /><testcase classname="tests.test_critical_logical_fixes.TestCriticalFixes" name="test_dd_penalty_no_duplication" time="0.001" /><testcase classname="tests.test_critical_logical_fixes.TestCriticalFixes" name="test_no_unreachable_code_after_raise" time="0.001" /><testcase classname="tests.test_critical_logical_fixes.TestLogicalFixes" name="test_zscore_exit_validation_fixed" time="0.001" /><testcase classname="tests.test_critical_logical_fixes.TestLogicalFixes" name="test_none_values_handling_fixed" time="0.000" /><testcase classname="tests.test_critical_logical_fixes.TestLogicalFixes" name="test_trading_days_calculation_fixed" time="0.001" /><testcase classname="tests.test_critical_logical_fixes.TestPerformanceOptimizations" name="test_intermediate_report_interval_optimized" time="0.000" /><testcase classname="tests.test_critical_logical_fixes.TestPerformanceOptimizations" name="test_pruner_parameters_standardized" time="0.001" /><testcase classname="tests.test_critical_logical_fixes.TestWinRateFix" name="test_win_rate_variable_fix" time="0.032" /><testcase classname="tests.test_critical_trade_counting_fixes.TestCriticalTradeCountingFixes" name="test_trade_counting_vs_bars_counting" time="0.003" /><testcase classname="tests.test_critical_trade_counting_fixes.TestCriticalTradeCountingFixes" name="test_trading_days_calculation_from_data" time="0.016" /><testcase classname="tests.test_critical_trade_counting_fixes.TestCriticalTradeCountingFixes" name="test_trades_per_day_calculation_accuracy" time="0.001" /><testcase classname="tests.test_critical_trade_counting_fixes.TestCriticalTradeCountingFixes" name="test_min_trades_threshold_logic" time="0.001" /><testcase classname="tests.test_critical_trade_counting_fixes.TestCriticalTradeCountingFixes" name="test_integration_all_counting_fixes" time="0.004" /><testcase classname="tests.test_final_audit_fixes_validation.TestAuditFixesValidation" name="test_1_normalization_lookahead_bias_fixed" time="0.014" /><testcase classname="tests.test_final_audit_fixes_validation.TestAuditFixesValidation" name="test_2_data_separation_train_test_fixed" time="0.038" /><testcase classname="tests.test_final_audit_fixes_validation.TestAuditFixesValidation" name="test_3_pruning_vs_penalty_strategy_fixed" time="0.032" /><testcase classname="tests.test_final_audit_fixes_validation.TestAuditFixesValidation" name="test_4_reproducibility_seeding_fixed" time="0.712" /><testcase classname="tests.test_final_audit_fixes_validation.TestAuditFixesValidation" name="test_5_parameter_dependencies_fixed" time="0.030" /><testcase classname="tests.test_final_audit_fixes_validation.TestAuditFixesValidation" name="test_6_no_duplicated_calculations_fixed" time="0.028" /><testcase classname="tests.test_final_audit_fixes_validation.TestAuditFixesValidation" name="test_7_comprehensive_integration_test" time="0.030" /><testcase classname="tests.test_metric_utils.TestExtractSharpe" name="test_extract_sharpe_ratio_abs" time="0.001" /><testcase classname="tests.test_metric_utils.TestExtractSharpe" name="test_extract_sharpe_ratio_fallback" time="0.001" /><testcase classname="tests.test_metric_utils.TestExtractSharpe" name="test_extract_sharpe_none_when_missing" time="0.000" /><testcase classname="tests.test_metric_utils.TestExtractSharpe" name="test_extract_sharpe_none_for_nan" time="0.000" /><testcase classname="tests.test_metric_utils.TestExtractSharpe" name="test_extract_sharpe_none_for_invalid_input" time="0.000" /><testcase classname="tests.test_metric_utils.TestExtractSharpe" name="test_extract_sharpe_handles_string_values" time="0.001" /><testcase classname="tests.test_metric_utils.TestNormalizeParams" name="test_normalize_short_names_to_canonical" time="0.001" /><testcase classname="tests.test_metric_utils.TestNormalizeParams" name="test_normalize_preserves_canonical_names" time="0.001" /><testcase classname="tests.test_metric_utils.TestNormalizeParams" name="test_normalize_mixed_names" time="0.001" /><testcase classname="tests.test_metric_utils.TestNormalizeParams" name="test_normalize_empty_params" time="0.001" /><testcase classname="tests.test_metric_utils.TestNormalizeParams" name="test_normalize_does_not_modify_original" time="0.001" /><testcase classname="tests.test_optimization_fixes.TestOptimizationFixes" name="test_simple_backtest_generates_trades" time="0.009" /><testcase classname="tests.test_optimization_fixes.TestOptimizationFixes" name="test_zscore_threshold_usage" time="0.002" /><testcase classname="tests.test_optimization_fixes.TestOptimizationFixes" name="test_data_loading_efficiency" time="0.032" /><testcase classname="tests.test_optimization_fixes" name="test_config_parameters" time="0.020" /><testcase classname="tests.test_optimization_recommendations.TestOptimizationRecommendations" name="test_cache_performance_optimization_recommendations" time="0.001" /><testcase classname="tests.test_optimization_recommendations.TestOptimizationRecommendations" name="test_concurrent_access_optimization_recommendations" time="0.001" /><testcase classname="tests.test_optimization_recommendations.TestOptimizationRecommendations" name="test_memory_optimization_recommendations" time="0.001" /><testcase classname="tests.test_optimization_recommendations.TestOptimizationRecommendations" name="test_integration_tests_optimization_recommendations" time="0.001" /><testcase classname="tests.test_optimization_recommendations.TestOptimizationRecommendations" name="test_pytest_configuration_recommendations" time="0.001" /><testcase classname="tests.test_optimization_recommendations.TestOptimizationRecommendations" name="test_specific_file_recommendations" time="0.001" /><testcase classname="tests.test_optimization_recommendations.TestOptimizationRecommendations" name="test_implementation_priority" time="0.001" /><testcase classname="tests.test_optimized_cache_performance.TestOptimizedCachePerformance" name="test_cache_logic_correctness_fast" time="0.000"><skipped type="pytest.skip" message="Требуется реальная реализация">/Users/<USER>/Desktop/coint4/tests/test_optimized_cache_performance.py:69: Требуется реальная реализация</skipped></testcase><testcase classname="tests.test_optimized_cache_performance.TestOptimizedCachePerformance" name="test_cache_usage_mocked" time="0.000"><skipped type="pytest.skip" message="Требуется реальная реализация">/Users/<USER>/Desktop/coint4/tests/test_optimized_cache_performance.py:223: Требуется реальная реализация</skipped></testcase><testcase classname="tests.test_optimized_cache_performance.TestOptimizedCachePerformance" name="test_performance_comparison_summary" time="0.001" /><testcase classname="tests.test_optuna_critical_fixes.TestOptunaCriticalFixes" name="test_intermediate_reports_use_accumulated_data" time="0.003" /><testcase classname="tests.test_optuna_critical_fixes.TestOptunaCriticalFixes" name="test_no_unreachable_code_after_trial_pruned" time="0.022" /><testcase classname="tests.test_optuna_critical_fixes.TestOptunaCriticalFixes" name="test_error_categorization_consistency" time="0.033" /><testcase classname="tests.test_optuna_critical_fixes.TestOptunaCriticalFixes" name="test_step_counter_consistency" time="0.005" /><testcase classname="tests.test_performance_analysis.TestPerformanceAnalysis" name="test_data_generation_optimization" time="0.004" /><testcase classname="tests.test_performance_analysis.TestPerformanceAnalysis" name="test_fixture_reuse_optimization" time="0.003" /><testcase classname="tests.test_performance_analysis.TestPerformanceAnalysis" name="test_mock_optimization" time="0.004" /><testcase classname="tests.test_performance_analysis.TestPerformanceAnalysis" name="test_data_size_optimization" time="0.004" /><testcase classname="tests.test_performance_analysis.TestPerformanceAnalysis" name="test_parallel_execution_concept" time="0.002" /><testcase classname="tests.test_performance_analysis.TestPerformanceAnalysis" name="test_caching_optimization_concept" time="0.002" /><testcase classname="tests.test_performance_analysis.TestPerformanceAnalysis" name="test_summary_optimization_recommendations" time="0.002" /><testcase classname="tests.test_trade_metrics_correctness.TestTradeMetricsCorrectness" name="test_avg_hold_time_calculated_correctly" time="0.007" /><testcase classname="tests.test_trade_metrics_correctness.TestTradeMetricsCorrectness" name="test_avg_trade_size_calculated_by_trades_not_bars" time="0.010" /><testcase classname="tests.test_trade_metrics_correctness.TestTradeMetricsCorrectness" name="test_daily_returns_resampling_for_15min_data" time="0.007" /><testcase classname="tests.test_trade_metrics_correctness.TestTradeMetricsCorrectness" name="test_positive_days_calculated_by_days_not_bars" time="0.008" /><testcase classname="tests.test_trade_metrics_correctness.TestTradeMetricsCorrectness" name="test_win_rate_calculated_by_trades_not_bars" time="0.004" /><testcase classname="tests.test_walk_forward_integration.TestWalkForwardIntegration" name="test_config_validation_and_loading" time="0.002" /><testcase classname="tests.core.test_40_data_loading" name="test_load_all_data_for_period" time="0.335" /><testcase classname="tests.core.test_40_data_loading" name="test_load_pair_data" time="0.045" /><testcase classname="tests.core.test_40_data_loading" name="test_load_and_normalize_data" time="0.060" /><testcase classname="tests.core.test_40_data_loading" name="test_clear_cache" time="0.131" /><testcase classname="tests.core.test_40_data_loading" name="test_fill_limit_pct_application" time="0.039" /><testcase classname="tests.core.test_40_data_loading" name="test__load_full_dataset" time="0.048" /><testcase classname="tests.core.test_40_data_loading" name="test_fill_limit_respects_large_gaps" time="0.072" /><testcase classname="tests.core.test_fast_coint" name="test_fast_coint_accuracy" time="0.063" /><testcase classname="tests.core.test_fast_coint" name="test_fast_coint_speed" time="0.083" /><testcase classname="tests.core.test_fast_coint" name="test_fast_coint_pandas_compatibility" time="0.025" /><testcase classname="tests.core.test_fast_coint" name="test_fast_coint_with_nan_values" time="0.092" /><testcase classname="tests.core.test_fast_coint" name="test_fast_coint_edge_cases" time="0.010" /><testcase classname="tests.core.test_file_glob" name="test_rglob_finds_all_files" time="0.094" /><testcase classname="tests.core.test_intraday_frequency" name="test_intraday_frequency" time="0.047" /><testcase classname="tests.core.test_math_utils" name="test_rolling_beta_matches_linregress" time="0.005" /><testcase classname="tests.core.test_math_utils" name="test_rolling_zscore_basic" time="0.002" /><testcase classname="tests.core.test_math_utils" name="test_calculate_ssd_block_based" time="0.008" /><testcase classname="tests.core.test_math_utils" name="test_calculate_half_life_deterministic" time="0.003" /><testcase classname="tests.core.test_math_utils" name="test_count_mean_crossings" time="0.001" /><testcase classname="tests.core.test_math_utils" name="test_half_life_numba" time="0.012" /><testcase classname="tests.core.test_math_utils" name="test_mean_crossings_numba" time="0.005" /><testcase classname="tests.core.test_pair_backtester_integration.TestPairBacktesterIntegration" name="test_pair_backtester_inherits_incremental_correctly" time="0.003" /><testcase classname="tests.core.test_pair_backtester_integration.TestPairBacktesterIntegration" name="test_run_on_day_incremental_behavior" time="0.050" /><testcase classname="tests.core.test_pair_backtester_integration.TestPairBacktesterIntegration" name="test_data_management_with_max_history_days" time="0.315" /><testcase classname="tests.core.test_pair_backtester_integration.TestPairBacktesterIntegration" name="test_portfolio_integration_with_real_calculations" time="0.115" /><testcase classname="tests.core.test_pair_backtester_integration.TestPairBacktesterIntegration" name="test_error_handling_and_edge_cases" time="0.007" /><testcase classname="tests.core.test_pair_backtester_integration.TestPairBacktesterIntegration" name="test_backward_compatibility_with_existing_interface" time="0.008" /><testcase classname="tests.core.test_performance" name="test_sharpe_ratio_uses_annualizing_factor" time="0.001" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_backtester_outputs" time="0.180" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_take_profit_logic" time="0.424" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_bid_ask_spread_costs" time="0.398" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_cost_validation" time="0.002" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_take_profit_with_reference" time="0.099" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_zero_std_handling" time="0.050" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_step_pnl_includes_costs" time="0.117" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_time_stop" time="0.206" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_division_by_zero_protection" time="0.011" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_parameter_validation" time="0.002" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_empty_data_handling" time="0.018" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_pnl_calculation_with_individual_asset_returns" time="0.079" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_pnl_formula_verification_with_manual_calculation" time="0.117" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_incremental_pnl_calculation_with_individual_asset_returns" time="0.018" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_ols_cache_memory_limit_with_15min_data" time="28.264" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_position_size_protection_against_microscopic_risk" time="0.444" /><testcase classname="tests.engine.test_01_backtest_engine_core" name="test_ols_cache_lru_behavior" time="0.501" /><testcase classname="tests.engine.test_02_backtest_engine_fixes.TestBacktestFixes" name="test_constant_data_handling" time="0.025" /><testcase classname="tests.engine.test_02_backtest_engine_fixes.TestBacktestFixes" name="test_zero_std_protection" time="0.005" /><testcase classname="tests.engine.test_02_backtest_engine_fixes.TestBacktestFixes" name="test_position_size_zero_division_protection" time="0.002" /><testcase classname="tests.engine.test_02_backtest_engine_fixes.TestBacktestFixes" name="test_take_profit_logic_correction" time="0.042" /><testcase classname="tests.engine.test_02_backtest_engine_fixes.TestBacktestFixes" name="test_time_calculation_consistency_15min" time="0.003" /><testcase classname="tests.engine.test_02_backtest_engine_fixes.TestBacktestFixes" name="test_cache_management_and_security" time="0.028" /><testcase classname="tests.engine.test_02_backtest_engine_fixes.TestBacktestFixes" name="test_parameter_validation_enhancements" time="0.004" /><testcase classname="tests.engine.test_02_backtest_engine_fixes.TestBacktestFixes" name="test_sharpe_ratio_calculation_accuracy" time="0.231" /><testcase classname="tests.engine.test_02_backtest_engine_fixes.TestBacktestFixes" name="test_integration_all_fixes" time="0.447" /><testcase classname="tests.engine.test_20_step_by_step_analysis" name="test_backtest_step_by_step" time="0.111" /><testcase classname="tests.engine.test_21_optimization_features.TestBacktestEngineOptimization" name="test_optimized_backtest_produces_valid_results" time="1.985" /><testcase classname="tests.engine.test_21_optimization_features.TestBacktestEngineOptimization" name="test_regime_detection_with_optimization" time="2.049" /><testcase classname="tests.engine.test_21_optimization_features.TestBacktestEngineOptimization" name="test_structural_break_protection_with_optimization" time="2.071" /><testcase classname="tests.engine.test_21_optimization_features.TestBacktestEngineOptimization" name="test_cache_effectiveness" time="4.095" /><testcase classname="tests.engine.test_21_optimization_features.TestBacktestEngineOptimization" name="test_frequency_optimization_consistency" time="3.679" /><testcase classname="tests.engine.test_21_optimization_features.TestBacktestEngineOptimization" name="test_lazy_adf_effectiveness" time="1.846" /><testcase classname="tests.engine.test_21_optimization_features.TestBacktestEngineOptimization" name="test_parameter_edge_cases" time="3.818" /><testcase classname="tests.engine.test_21_optimization_features.TestBacktestEngineOptimization" name="test_optimization_with_real_config" time="1.759" /><testcase classname="tests.engine.test_21_optimization_features.TestBacktestEngineOptimization" name="test_memory_usage_optimization" time="6.795" /><testcase classname="tests.engine.test_21_optimization_features.TestBacktestEngineOptimization" name="test_error_handling_with_optimizations" time="1.672" /><testcase classname="tests.engine.test_enhanced_risk_management.TestEnhancedRiskManagement" name="test_kelly_sizing_calculation" time="0.002" /><testcase classname="tests.engine.test_enhanced_risk_management.TestEnhancedRiskManagement" name="test_adaptive_threshold_calculation" time="0.002" /><testcase classname="tests.engine.test_enhanced_risk_management.TestEnhancedRiskManagement" name="test_var_position_sizing" time="0.002" /><testcase classname="tests.engine.test_enhanced_risk_management.TestEnhancedRiskManagement" name="test_enhanced_risk_management_integration" time="0.396" /><testcase classname="tests.engine.test_enhanced_risk_management.TestEnhancedRiskManagement" name="test_parameter_validation_enhanced" time="0.003" /><testcase classname="tests.engine.test_enhanced_risk_management.TestEnhancedRiskManagement" name="test_kelly_sizing_with_insufficient_data" time="0.001" /><testcase classname="tests.engine.test_enhanced_risk_management.TestEnhancedRiskManagement" name="test_adaptive_thresholds_disabled" time="0.001" /><testcase classname="tests.engine.test_market_regime_detection.TestMarketRegimeDetection" name="test_hurst_exponent_calculation" time="0.002" /><testcase classname="tests.engine.test_market_regime_detection.TestMarketRegimeDetection" name="test_variance_ratio_calculation" time="0.003" /><testcase classname="tests.engine.test_market_regime_detection.TestMarketRegimeDetection" name="test_market_regime_detection_integration" time="0.958" /><testcase classname="tests.engine.test_market_regime_detection.TestMarketRegimeDetection" name="test_market_regime_trading_restrictions" time="1.360" /><testcase classname="tests.engine.test_market_regime_detection.TestStructuralBreakProtection" name="test_rolling_correlation_calculation" time="0.002" /><testcase classname="tests.engine.test_market_regime_detection.TestStructuralBreakProtection" name="test_half_life_calculation" time="0.004" /><testcase classname="tests.engine.test_market_regime_detection.TestStructuralBreakProtection" name="test_adf_test_calculation" time="0.008" /><testcase classname="tests.engine.test_market_regime_detection.TestStructuralBreakProtection" name="test_structural_break_detection_integration" time="0.928" /><testcase classname="tests.engine.test_market_regime_detection.TestStructuralBreakProtection" name="test_structural_break_position_closure" time="0.817" /><testcase classname="tests.engine.test_market_regime_detection.TestStructuralBreakProtection" name="test_parameter_validation_for_new_features" time="0.001" /><testcase classname="tests.engine.test_market_regime_optimization.TestMarketRegimeOptimization" name="test_numba_hurst_exponent_accuracy" time="0.000" /><testcase classname="tests.engine.test_market_regime_optimization.TestMarketRegimeOptimization" name="test_numba_variance_ratio_accuracy" time="0.001" /><testcase classname="tests.engine.test_market_regime_optimization.TestMarketRegimeOptimization" name="test_numba_rolling_correlation_accuracy" time="0.000" /><testcase classname="tests.engine.test_market_regime_optimization.TestMarketRegimeOptimization" name="test_market_regime_cache_functionality" time="0.002" /><testcase classname="tests.engine.test_market_regime_optimization.TestMarketRegimeOptimization" name="test_regime_check_frequency_optimization" time="0.003" /><testcase classname="tests.engine.test_market_regime_optimization.TestMarketRegimeOptimization" name="test_lazy_adf_optimization" time="0.003" /><testcase classname="tests.engine.test_market_regime_optimization.TestMarketRegimeOptimization" name="test_cache_memory_management" time="0.111" /><testcase classname="tests.engine.test_market_regime_optimization.TestMarketRegimeOptimization" name="test_configuration_parameter_validation" time="0.002" /><testcase classname="tests.engine.test_market_regime_optimization.TestMarketRegimeOptimization" name="test_regime_consistency_with_frequency_optimization" time="0.731" /><testcase classname="tests.engine.test_max_positions_increase.TestMaxPositionsIncrease" name="test_portfolio_max_positions_15" time="0.000" /><testcase classname="tests.engine.test_max_positions_increase.TestMaxPositionsIncrease" name="test_config_max_positions_validation" time="0.000" /><testcase classname="tests.engine.test_max_positions_increase.TestMaxPositionsIncrease" name="test_diversification_benefits_with_15_positions" time="0.001" /><testcase classname="tests.engine.test_max_positions_increase.TestMaxPositionsIncrease" name="test_position_sizing_with_15_positions" time="0.000" /><testcase classname="tests.engine.test_max_positions_increase.TestMaxPositionsIncrease" name="test_memory_efficiency_with_15_positions" time="0.000" /><testcase classname="tests.engine.test_max_positions_increase.TestMaxPositionsIncrease" name="test_risk_management_with_15_positions" time="0.000" /><testcase classname="tests.engine.test_max_positions_increase.TestMaxPositionsIncrease" name="test_performance_scaling_with_15_positions" time="0.001" /><testcase classname="tests.engine.test_portfolio_position_limits.TestPortfolioPositionLimits" name="test_max_active_positions_limits_concurrent_positions" time="0.000" /><testcase classname="tests.engine.test_portfolio_position_limits.TestPortfolioPositionLimits" name="test_pair_backtester_respects_portfolio_limits" time="0.134" /><testcase classname="tests.engine.test_portfolio_position_limits.TestPortfolioPositionLimits" name="test_portfolio_position_tracking_accuracy" time="0.001" /><testcase classname="tests.engine.test_portfolio_position_limits.TestPortfolioPositionLimits" name="test_portfolio_handles_duplicate_operations_gracefully" time="0.001" /><testcase classname="tests.engine.test_trades_log_fix.TestTradesLogFix" name="test_trades_log_contains_complete_trades" time="0.418" /><testcase classname="tests.engine.test_trades_log_fix.TestTradesLogFix" name="test_incremental_trades_log_to_complete_conversion" time="0.263" /><testcase classname="tests.engine.test_trades_log_fix.TestTradesLogFix" name="test_performance_metrics_with_complete_trades" time="0.495" /><testcase classname="tests.engine.test_volatility_based_sizing.TestVolatilityBasedSizing" name="test_volatility_multiplier_calculation" time="0.005" /><testcase classname="tests.engine.test_volatility_based_sizing.TestVolatilityBasedSizing" name="test_volatility_based_position_sizing_integration" time="0.003" /><testcase classname="tests.engine.test_volatility_based_sizing.TestVolatilityBasedSizing" name="test_volatility_multiplier_bounds" time="0.004" /><testcase classname="tests.engine.test_volatility_based_sizing.TestVolatilityBasedSizing" name="test_insufficient_data_handling" time="0.002" /><testcase classname="tests.engine.test_volatility_based_sizing.TestVolatilityBasedSizing" name="test_zero_volatility_handling" time="0.003" /><testcase classname="tests.engine.test_volatility_based_sizing.TestVolatilityBasedSizing" name="test_config_parameter_validation" time="0.005" /><testcase classname="tests.pipeline.test_filters_beta" name="test_filter_pairs_beta_range" time="0.007" /><testcase classname="tests.pipeline.test_pair_scanner_integration" name="test_find_cointegrated_pairs" time="0.040"><skipped type="pytest.skip" message="No data loaded for test">/Users/<USER>/Desktop/coint4/tests/pipeline/test_pair_scanner_integration.py:78: No data loaded for test</skipped></testcase><testcase classname="tests.utils.test_41_config_validation" name="test_load_config" time="0.007" /><testcase classname="tests.utils.test_41_config_validation" name="test_fill_limit_pct_validation" time="0.001" /><testcase classname="tests.utils.test_42_time_utilities" name="test_ensure_datetime_index_sorts_and_drops_tz" time="0.002" /><testcase classname="tests.utils.test_42_time_utilities" name="test_infer_frequency_regular[D-D]" time="0.002" /><testcase classname="tests.utils.test_42_time_utilities" name="test_infer_frequency_regular[h-h]" time="0.001" /><testcase classname="tests.utils.test_42_time_utilities" name="test_infer_frequency_regular[15min-15min]" time="0.001" /><testcase classname="tests.utils.test_42_time_utilities" name="test_infer_frequency_irregular" time="0.005" /></testsuite></testsuites>