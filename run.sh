#!/bin/bash
# Простой скрипт для запуска полного пайплайна

echo "🚀 ЗАПУСК ПОЛНОГО ПАЙПЛАЙНА"
echo "=================================================="
echo "📋 Этапы:"
echo "   1. Optuna оптимизация (5 trials)"
echo "   2. Walk-forward валидация"
echo "   3. Обновление веб-интерфейса"
echo "=================================================="

# Запускаем пайплайн
python run_full_pipeline.py

# Проверяем результат
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 ПАЙПЛАЙН ЗАВЕРШЕН УСПЕШНО!"
    echo "🌐 Откройте веб-интерфейс:"
    echo "   file://$(pwd)/src/web_analysis/index.html"
    echo ""
    echo "💡 Для повторного запуска используйте:"
    echo "   ./run.sh"
    echo "   или"
    echo "   python run.py"
else
    echo ""
    echo "💥 ОШИБКА ВЫПОЛНЕНИЯ ПАЙПЛАЙНА"
    exit 1
fi
