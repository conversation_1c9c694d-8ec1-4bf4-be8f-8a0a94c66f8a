#!/bin/bash
# Супер-быстрые тесты - только критически важные проверки

set -e

echo "🚀 СУПЕР-БЫСТРЫЕ ТЕСТЫ"
echo "====================="
echo "Цель: Только критически важные проверки за <15 секунд"
echo ""

# Переходим в корневую директорию проекта
cd "$(dirname "$0")/.."

# Только smoke тесты - самые критичные
echo "1️⃣ Smoke тесты (критически важные)..."
time pytest -m smoke --maxfail=1 -q --tb=short
echo "✅ Smoke тесты пройдены"
echo ""

# Только наши консолидированные тесты критических исправлений
echo "2️⃣ Консолидированные тесты критических исправлений..."
time pytest tests/test_critical_fixes_consolidated.py --maxfail=2 -q --tb=short
echo "✅ Тесты критических исправлений пройдены"
echo ""

# Несколько быстрых core тестов (без интеграционных)
echo "3️⃣ Быстрые core тесты..."
time pytest \
    tests/core/test_math_utils.py \
    tests/core/test_performance.py \
    tests/utils/test_42_time_utilities.py \
    --maxfail=2 -q --tb=short
echo "✅ Core тесты пройдены"
echo ""

echo "🎉 СУПЕР-БЫСТРЫЕ ТЕСТЫ ЗАВЕРШЕНЫ!"
echo "Система готова для базовой работы."
