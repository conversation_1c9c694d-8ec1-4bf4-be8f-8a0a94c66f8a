#!/bin/bash
# Запускает ПОЛНЫЙ набор тестов, включая медленные и последовательные.
# Используется для ночных сборок или перед релизами.
set -e
echo "🐢 Запуск FULL набора тестов (может занять время)..."
cd "$(dirname "$0")/.."

echo "1/2: Запуск всех параллелизуемых тестов..."
pytest -n auto -m "not serial"

echo "2/2: Запуск всех последовательных тестов..."
pytest -m serial

echo "✅ Полный набор тестов пройден!"
