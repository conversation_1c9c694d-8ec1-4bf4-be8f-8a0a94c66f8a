#!/bin/bash
# Запускает все быстрые тесты, подходящие для CI/CD.
# Исключает 'slow' и выполняет 'serial' тесты отдельно.
# Время выполнения: < 1 минуты.
set -e
echo "⚡ Запуск FAST тестов..."
cd "$(dirname "$0")/.."

echo "1/2: Запуск параллелизуемых быстрых тестов..."
pytest -n auto -m "fast and not serial"

echo "2/2: Запуск последовательных быстрых тестов..."
pytest -m "fast and serial"

echo "✅ Все быстрые тесты пройдены!"
