#!/bin/bash
# Development tests - только измененные файлы и быстрые тесты

set -e

echo "⚡ DEVELOPMENT TESTS"
echo "==================="
echo "Цель: Быстрая проверка во время разработки"
echo "Фокус: Последние неудачные тесты + быстрые тесты"
echo ""

# Переходим в корневую директорию проекта
cd "$(dirname "$0")/.."

# Сначала запускаем последние неудачные тесты
echo "1️⃣ Запуск последних неудачных тестов..."
if pytest --lf -x -q --tb=short; then
    echo "✅ Все ранее неудачные тесты теперь проходят"
else
    echo "❌ Есть неудачные тесты - исправьте их перед продолжением"
    exit 1
fi
echo ""

# Быстрые тесты параллельно
echo "2️⃣ Запуск быстрых тестов..."
time pytest -n auto -m "fast and not serial and not slow" --maxfail=3 -q --tb=short
echo "✅ Быстрые тесты пройдены"
echo ""

# Smoke тесты для уверенности
echo "3️⃣ Финальная проверка smoke тестов..."
pytest -m smoke --maxfail=1 -q --tb=short
echo "✅ Smoke тесты пройдены"
echo ""

echo "🎯 DEVELOPMENT TESTS ЗАВЕРШЕНЫ!"
echo "Код готов для коммита."
