#!/bin/bash
# CI/CD pipeline tests - быстрые + некоторые интеграционные тесты

set -e

echo "🚀 CI/CD PIPELINE TESTS"
echo "======================="
echo "Цель: Полная проверка для CI/CD без медленных тестов"
echo "Время выполнения: <5 минут"
echo ""

# Переходим в корневую директорию проекта
cd "$(dirname "$0")/.."

# Сначала smoke тесты
echo "1️⃣ Запуск smoke тестов..."
time pytest -m smoke --maxfail=1 -q --tb=short
echo "✅ Smoke тесты пройдены"
echo ""

# Быстрые unit тесты параллельно (исключаем интеграционные)
echo "2️⃣ Запуск быстрых unit тестов (параллельно)..."
time pytest -n auto -m "fast and not serial and not integration and not slow" --maxfail=5 -q --tb=short --durations=5
echo "✅ Быстрые unit тесты пройдены"
echo ""

# Критические исправления (только smoke версии)
echo "3️⃣ Запуск smoke тестов критических исправлений..."
time pytest -m "critical_fixes and smoke" --maxfail=3 -q --tb=short
echo "✅ Smoke тесты критических исправлений пройдены"
echo ""

# Несколько важных интеграционных тестов последовательно
echo "4️⃣ Запуск важных интеграционных тестов (последовательно)..."
time pytest tests/test_critical_fixes_consolidated.py tests/test_smoke.py --maxfail=2 -q --tb=short
echo "✅ Важные интеграционные тесты пройдены"
echo ""

echo "🎉 CI/CD PIPELINE TESTS ЗАВЕРШЕНЫ УСПЕШНО!"
echo "Система готова для продакшена."
