"""Тесты для run_optimization.py (консолидированные из fix файлов)."""

import pytest
import pandas as pd
import numpy as np
from pathlib import Path
import tempfile
import shutil
from unittest.mock import Mock, patch, mock_open

from src.optimiser.run_optimization import run_optimization


class TestRunOptimizationUnit:
    """Быстрые unit тесты для run_optimization."""
    
    @pytest.mark.unit
    def test_configuration_saving_logic(self):
        """Unit test: проверяем логику сохранения конфигурации (Task 4 fix)."""
        # Мокаем конфигурацию
        mock_config = {
            'optimization': {
                'n_trials': 10,
                'timeout_seconds': 300,
                'n_jobs': 1
            },
            'output': {
                'results_dir': 'results',
                'save_config': True
            }
        }
        
        # Проверяем параметры сохранения
        assert 'output' in mock_config, "Должна быть секция output"
        assert 'save_config' in mock_config['output'], "Должен быть параметр save_config"
        
        # Проверяем логику определения пути сохранения
        results_dir = mock_config['output']['results_dir']
        config_path = Path(results_dir) / 'config.yaml'
        
        assert isinstance(config_path, Path), "Путь должен быть объектом Path"
        assert config_path.suffix == '.yaml', "Конфигурация должна сохраняться в YAML"
    
    @pytest.mark.unit
    def test_optimization_parameters_validation(self):
        """Unit test: проверяем валидацию параметров оптимизации."""
        # Тестируем различные параметры оптимизации
        optimization_params = {
            'n_trials': 100,
            'timeout_seconds': 3600,
            'n_jobs': -1,
            'sampler': 'TPE',
            'pruner': 'MedianPruner'
        }
        
        # Проверяем разумность параметров
        assert optimization_params['n_trials'] > 0, "n_trials должен быть положительным"
        assert optimization_params['timeout_seconds'] > 0, "timeout должен быть положительным"
        assert optimization_params['n_jobs'] != 0, "n_jobs не должен быть нулем"
        
        # Проверяем типы
        assert isinstance(optimization_params['n_trials'], int)
        assert isinstance(optimization_params['timeout_seconds'], int)
        assert isinstance(optimization_params['n_jobs'], int)
    
    @pytest.mark.unit
    def test_results_directory_creation_logic(self):
        """Unit test: проверяем логику создания директории результатов."""
        with tempfile.TemporaryDirectory() as temp_dir:
            results_dir = Path(temp_dir) / 'test_results'
            
            # Проверяем, что директория не существует
            assert not results_dir.exists(), "Директория не должна существовать изначально"
            
            # Симулируем создание директории
            results_dir.mkdir(parents=True, exist_ok=True)
            
            # Проверяем, что директория создана
            assert results_dir.exists(), "Директория должна быть создана"
            assert results_dir.is_dir(), "Должна быть директорией"
    
    @pytest.mark.unit
    def test_study_name_generation_logic(self):
        """Unit test: проверяем логику генерации имени исследования."""
        import datetime
        
        # Симулируем генерацию имени исследования
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        study_name = f"optimization_{timestamp}"
        
        # Проверяем формат имени
        assert study_name.startswith("optimization_"), "Имя должно начинаться с optimization_"
        assert len(study_name) > len("optimization_"), "Имя должно содержать timestamp"
        assert "_" in study_name, "Имя должно содержать разделитель"


class TestRunOptimizationIntegration:
    """Медленные integration тесты для run_optimization."""
    
    @pytest.mark.slow
    @pytest.mark.integration
    def test_full_optimization_pipeline(self):
        """Integration test: проверяем полный пайплайн оптимизации."""
        # Проверяем наличие необходимых файлов
        required_files = [
            "configs/main_2024.yaml",
            "configs/search_space_fast.yaml"
        ]
        
        for file_path in required_files:
            if not Path(file_path).exists():
                pytest.skip(f"Файл {file_path} не найден")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            try:
                # Мокаем аргументы командной строки
                test_args = {
                    'base_config': 'configs/main_2024.yaml',
                    'search_space': 'configs/search_space_fast.yaml',
                    'n_trials': 2,  # Минимальное количество для быстрого теста
                    'timeout': 60,  # 1 минута максимум
                    'n_jobs': 1,
                    'results_dir': temp_dir,
                    'study_name': 'test_study'
                }
                
                # Запускаем оптимизацию с минимальными параметрами
                with patch('sys.argv', ['run_optimization.py'] + [f'--{k}={v}' for k, v in test_args.items()]):
                    result = run_optimization(
                        base_config_path=test_args['base_config'],
                        search_space_path=test_args['search_space'],
                        n_trials=test_args['n_trials'],
                        timeout_seconds=test_args['timeout'],
                        n_jobs=test_args['n_jobs'],
                        results_dir=test_args['results_dir'],
                        study_name=test_args['study_name']
                    )
                
                # Проверяем результат
                assert result is not None, "Результат оптимизации не должен быть None"
                
                # Проверяем, что файлы результатов созданы
                results_path = Path(temp_dir)
                assert results_path.exists(), "Директория результатов должна существовать"
                
            except Exception as e:
                # Если тест падает из-за отсутствия данных, это нормально
                if any(keyword in str(e).lower() for keyword in ['data', 'file', 'path']):
                    pytest.skip(f"Тест пропущен из-за отсутствия данных: {str(e)}")
                else:
                    pytest.fail(f"Неожиданная ошибка в оптимизации: {str(e)}")
    
    @pytest.mark.slow
    @pytest.mark.integration
    def test_configuration_saving_integration(self):
        """Integration test: проверяем сохранение конфигурации в реальной системе."""
        with tempfile.TemporaryDirectory() as temp_dir:
            results_dir = Path(temp_dir) / 'results'
            results_dir.mkdir(parents=True, exist_ok=True)
            
            # Создаем тестовую конфигурацию
            test_config = {
                'test_parameter': 'test_value',
                'optimization': {
                    'n_trials': 5,
                    'timeout_seconds': 30
                }
            }
            
            # Симулируем сохранение конфигурации
            config_path = results_dir / 'saved_config.yaml'
            
            try:
                import yaml
                with open(config_path, 'w') as f:
                    yaml.dump(test_config, f)
                
                # Проверяем, что файл создан
                assert config_path.exists(), "Файл конфигурации должен быть создан"
                
                # Проверяем, что можем прочитать конфигурацию обратно
                with open(config_path, 'r') as f:
                    loaded_config = yaml.safe_load(f)
                
                assert loaded_config == test_config, "Загруженная конфигурация должна совпадать с исходной"
                
            except ImportError:
                pytest.skip("PyYAML не установлен")
            except Exception as e:
                pytest.fail(f"Ошибка при сохранении/загрузке конфигурации: {str(e)}")
