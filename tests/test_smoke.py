#!/usr/bin/env python3
"""
SMOKE TESTS - Критически важные быстрые проверки

Цель: Убедиться что система не сломана на базовом уровне.
Время выполнения: <5 секунд общее время.

Smoke тесты проверяют:
1. Импорт всех основных модулей
2. Загрузка основной конфигурации  
3. Создание основных объектов без ошибок
4. Базовая работа на минимальных данных
"""

import pytest
import pandas as pd
import numpy as np
from pathlib import Path
import sys

# Добавляем путь к src
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Маркируем все тесты как smoke
pytestmark = pytest.mark.smoke


class TestSmokeImports:
    """Smoke тесты импортов - самые критичные проверки."""

    def test_import_when_config_utils_then_success(self):
        """Проверка импорта утилит конфигурации."""
        from src.coint2.utils.config import load_config
        assert callable(load_config)

    def test_import_when_performance_core_then_success(self):
        """Проверка импорта модулей производительности."""
        from src.coint2.core.performance import sharpe_ratio, max_drawdown
        assert callable(sharpe_ratio)
        assert callable(max_drawdown)

    def test_import_when_base_engine_then_success(self):
        """Проверка импорта базового движка."""
        from src.coint2.engine.base_engine import BasePairBacktester
        assert BasePairBacktester is not None

    def test_import_when_fast_objective_then_success(self):
        """Проверка импорта быстрой целевой функции."""
        from src.optimiser.fast_objective import FastWalkForwardObjective
        assert FastWalkForwardObjective is not None

    def test_import_when_run_optimization_then_success(self):
        """Проверка импорта модуля запуска оптимизации."""
        from src.optimiser.run_optimization import run_optimization
        assert callable(run_optimization)

    def test_import_when_portfolio_simulator_then_success(self):
        """Проверка импорта симулятора портфеля."""
        from src.coint2.pipeline.walk_forward_orchestrator import _simulate_realistic_portfolio
        assert callable(_simulate_realistic_portfolio)

    def test_import_when_pair_scanner_then_success(self):
        """Проверка импорта сканера пар."""
        from src.coint2.pipeline.pair_scanner import find_cointegrated_pairs
        assert callable(find_cointegrated_pairs)


class TestSmokeConfiguration:
    """Smoke тесты конфигурации."""

    def test_config_when_main_file_then_loads_successfully(self):
        """Проверка загрузки основной конфигурации."""
        config_path = Path("configs/main_2024.yaml")
        if not config_path.exists():
            pytest.skip("Основная конфигурация не найдена")

        from src.coint2.utils.config import load_config
        config = load_config(str(config_path))
        assert config is not None

    def test_config_when_loaded_then_has_backtest_section(self):
        """Проверка наличия секции backtest в конфигурации."""
        config_path = Path("configs/main_2024.yaml")
        if not config_path.exists():
            pytest.skip("Основная конфигурация не найдена")

        from src.coint2.utils.config import load_config
        config = load_config(str(config_path))
        assert hasattr(config, 'backtest'), "Секция backtest отсутствует"

    def test_config_when_loaded_then_has_portfolio_section(self):
        """Проверка наличия секции portfolio в конфигурации."""
        config_path = Path("configs/main_2024.yaml")
        if not config_path.exists():
            pytest.skip("Основная конфигурация не найдена")

        from src.coint2.utils.config import load_config
        config = load_config(str(config_path))
        assert hasattr(config, 'portfolio'), "Секция portfolio отсутствует"

    def test_config_when_portfolio_section_then_has_initial_capital(self):
        """Проверка наличия initial_capital в секции portfolio."""
        config_path = Path("configs/main_2024.yaml")
        if not config_path.exists():
            pytest.skip("Основная конфигурация не найдена")

        from src.coint2.utils.config import load_config
        config = load_config(str(config_path))
        assert hasattr(config.portfolio, 'initial_capital'), "initial_capital отсутствует"

    def test_config_when_portfolio_section_then_has_max_positions(self):
        """Проверка наличия max_active_positions в секции portfolio."""
        config_path = Path("configs/main_2024.yaml")
        if not config_path.exists():
            pytest.skip("Основная конфигурация не найдена")

        from src.coint2.utils.config import load_config
        config = load_config(str(config_path))
        assert hasattr(config.portfolio, 'max_active_positions'), "max_active_positions отсутствует"


class TestSmokeBasicFunctionality:
    """Smoke тесты базовой функциональности."""
    
    def test_pair_backtester_creation(self, tiny_prices_df):
        """Проверка создания BasePairBacktester без ошибок."""

        try:
            from src.coint2.engine.base_engine import BasePairBacktester

            # Создаем минимальный backtester с правильными параметрами
            backtester = BasePairBacktester(
                pair_data=tiny_prices_df,
                rolling_window=10,
                z_threshold=2.0,
                z_exit=0.5,
                commission_pct=0.001,
                slippage_pct=0.001,
                pair_name="TEST_PAIR"
            )

            assert backtester is not None, "BasePairBacktester не создался"
            assert backtester.pair_name == "TEST_PAIR", "Имя пары не установилось"



        except Exception as e:
            pytest.fail(f"Не удалось создать BasePairBacktester: {e}")
    
    def test_performance_metrics_calculation(self, tiny_prices_df):
        """Проверка расчета базовых метрик производительности."""
        
        try:
            from src.coint2.core.performance import sharpe_ratio, max_drawdown, win_rate
            
            # Создаем тестовые данные PnL
            pnl_series = pd.Series([10, -5, 15, -8, 20, -3, 12, -7, 18, -4])
            
            # Рассчитываем метрики
            sharpe = sharpe_ratio(pnl_series, annualizing_factor=252)
            max_dd = max_drawdown(pnl_series)
            wr = win_rate(pnl_series)
            
            # Проверяем что метрики разумные
            assert isinstance(sharpe, (int, float)), "Sharpe ratio должен быть числом"
            assert isinstance(max_dd, (int, float)), "Max drawdown должен быть числом"
            assert isinstance(wr, (int, float)), "Win rate должен быть числом"
            assert 0 <= wr <= 1, f"Win rate должен быть между 0 и 1, получен {wr}"
            

            
        except Exception as e:
            pytest.fail(f"Ошибка расчета метрик: {e}")
    
    def test_realistic_portfolio_simulation_exists(self, tiny_prices_df):
        """Проверка существования функции реалистичной симуляции портфеля."""
        
        try:
            from src.coint2.pipeline.walk_forward_orchestrator import _simulate_realistic_portfolio
            
            assert callable(_simulate_realistic_portfolio), \
                "Функция _simulate_realistic_portfolio должна быть вызываемой"
            

            
        except ImportError as e:
            pytest.fail(f"Функция реалистичной симуляции портфеля не найдена: {e}")


class TestSmokeDataHandling:
    """Smoke тесты обработки данных."""

    def test_dataframe_when_tiny_prices_then_valid_structure(self, tiny_prices_df):
        """Проверка структуры тестовых данных."""
        assert isinstance(tiny_prices_df, pd.DataFrame)
        assert len(tiny_prices_df) > 0
        assert len(tiny_prices_df.columns) >= 2

    def test_index_when_tiny_prices_then_datetime_type(self, tiny_prices_df):
        """Проверка типа индекса данных."""
        assert isinstance(tiny_prices_df.index, pd.DatetimeIndex)

    def test_statistics_when_tiny_prices_then_computable(self, tiny_prices_df):
        """Проверка возможности расчета базовых статистик."""
        means = tiny_prices_df.mean()
        stds = tiny_prices_df.std()

        assert len(means) == len(tiny_prices_df.columns)
        assert len(stds) == len(tiny_prices_df.columns)
        assert all(stds > 0), "Все стандартные отклонения должны быть положительными"


class TestSmokeBacktesting:
    """Smoke тесты бэктестинга."""

    def test_backtester_when_minimal_params_then_initializes(self, small_prices_df):
        """Проверка инициализации бэктестера с минимальными параметрами."""
        from src.coint2.engine.base_engine import BasePairBacktester

        params = {
            'rolling_window': 10,  # Уменьшили для совместимости с данными
            'z_threshold': 2.0,
            'z_exit': 0.5,
            'cooldown_periods': 4,
            'commission_pct': 0.001,
            'slippage_pct': 0.0005
        }

        backtester = BasePairBacktester(
            pair_data=small_prices_df.iloc[:, :2],
            **params
        )

        assert backtester is not None

    def test_backtest_when_executed_then_returns_results(self, small_prices_df):
        """Проверка получения результатов при выполнении бэктеста."""
        from src.coint2.engine.base_engine import BasePairBacktester

        params = {
            'rolling_window': 10,
            'z_threshold': 2.0,
            'z_exit': 0.5,
            'cooldown_periods': 4,
            'commission_pct': 0.001,
            'slippage_pct': 0.0005
        }

        backtester = BasePairBacktester(
            pair_data=small_prices_df.iloc[:, :2],
            **params
        )

        backtester.run()
        assert backtester.results is not None

    def test_results_when_backtest_complete_then_has_total_return(self, small_prices_df):
        """Проверка наличия total_return в результатах бэктеста."""
        from src.coint2.engine.base_engine import BasePairBacktester

        params = {
            'rolling_window': 10,
            'z_threshold': 2.0,
            'z_exit': 0.5,
            'cooldown_periods': 4,
            'commission_pct': 0.001,
            'slippage_pct': 0.0005
        }

        backtester = BasePairBacktester(
            pair_data=small_prices_df.iloc[:, :2],
            **params
        )

        backtester.run()
        # Проверяем, что результаты - это DataFrame с нужными колонками
        assert isinstance(backtester.results, pd.DataFrame)


class TestSmokeSystemReadiness:
    """Smoke тесты готовности системы."""

    def test_directories_when_required_then_exist(self):
        """Проверка существования обязательных директорий."""
        required_dirs = ["src", "configs", "tests", "scripts"]

        for dir_name in required_dirs:
            assert Path(dir_name).exists(), f"Директория {dir_name} должна существовать"

    def test_files_when_critical_then_exist(self):
        """Проверка существования критически важных файлов."""
        required_files = [
            "configs/main_2024.yaml",
            "src/optimiser/fast_objective.py",
            "src/coint2/engine/base_engine.py",
            "pytest.ini"
        ]

        for file_path in required_files:
            if Path(file_path).exists():
                assert Path(file_path).exists(), f"Файл {file_path} должен существовать"
            else:
                pytest.skip(f"Файл {file_path} не найден - пропускаем проверку")


# Все тесты запускаются только через pytest
