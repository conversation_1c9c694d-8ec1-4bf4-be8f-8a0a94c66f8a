#!/usr/bin/env python3
"""
Тест для проверки консистентности между оптимизатором и валидатором.

КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Проверяет что оптимизатор и валидатор используют одинаковую логику:
1. Реалистичную симуляцию портфеля с учетом max_active_positions
2. Корректный расчет final_score с использованием win_rate вместо positive_days_rate
3. Унифицированную логику агрегации PnL
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, mock_open
import sys
import os

# Добавляем путь к src для импорта модулей
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from coint2.utils.config import AppConfig
from coint2.pipeline.walk_forward_orchestrator import _simulate_realistic_portfolio
from optimiser.fast_objective import FastWalkForwardObjective


class TestOptimizerValidatorConsistency:
    """Тесты консистентности между оптимизатором и валидатором."""
    
    def setup_method(self):
        """Настройка тестового окружения."""
        # Создаем минимальную конфигурацию
        self.cfg = Mock()
        self.cfg.portfolio = Mock()
        self.cfg.portfolio.max_active_positions = 3
        self.cfg.portfolio.initial_capital = 100000
        
        # Создаем тестовые PnL серии
        dates = pd.date_range('2024-01-01', periods=100, freq='15min')
        
        # Пара 1: Хорошая производительность
        self.pnl_series_1 = pd.Series([10, 0, 0, 15, 0, -5, 0, 20, 0, 0] * 10, index=dates)
        
        # Пара 2: Средняя производительность  
        self.pnl_series_2 = pd.Series([5, 0, -3, 0, 8, 0, 0, -2, 0, 12] * 10, index=dates)
        
        # Пара 3: Плохая производительность
        self.pnl_series_3 = pd.Series([-8, 0, 0, 3, 0, -10, 0, 0, 5, 0] * 10, index=dates)
        
        # Пара 4: Очень активная (должна быть ограничена лимитом позиций)
        self.pnl_series_4 = pd.Series([2, -1, 3, -2, 1, -3, 4, -1, 2, -2] * 10, index=dates)
        
        # Пара 5: Еще одна активная пара
        self.pnl_series_5 = pd.Series([1, -2, 4, -1, 3, -2, 1, -3, 2, -1] * 10, index=dates)
        
        self.all_pnl_series = [
            self.pnl_series_1, self.pnl_series_2, self.pnl_series_3, 
            self.pnl_series_4, self.pnl_series_5
        ]
    
    def test_realistic_portfolio_simulation_function_exists(self):
        """Проверяет что функция _simulate_realistic_portfolio существует в walk_forward_orchestrator."""
        # Проверяем что функция импортируется
        assert callable(_simulate_realistic_portfolio), \
            "Функция _simulate_realistic_portfolio должна существовать в walk_forward_orchestrator.py"
        
        print("✅ Функция _simulate_realistic_portfolio найдена в walk_forward_orchestrator.py")
    
    def test_realistic_portfolio_simulation_logic(self):
        """Проверяет корректность логики реалистичной симуляции портфеля."""
        # Тестируем функцию симуляции портфеля
        portfolio_pnl = _simulate_realistic_portfolio(self.all_pnl_series, self.cfg)
        
        # Проверяем что результат не пустой
        assert not portfolio_pnl.empty, "Результат симуляции портфеля не должен быть пустым"
        
        # Проверяем что результат - это pandas Series
        assert isinstance(portfolio_pnl, pd.Series), "Результат должен быть pandas.Series"
        
        # Проверяем что индекс соответствует исходным данным
        expected_index = self.all_pnl_series[0].index
        assert portfolio_pnl.index.equals(expected_index), "Индекс результата должен соответствовать исходным данным"
        
        print(f"✅ Реалистичная симуляция портфеля работает корректно")
        print(f"   📊 Размер результата: {len(portfolio_pnl)} записей")
        print(f"   💰 Общий PnL: {portfolio_pnl.sum():.2f}")
    
    def test_position_limit_enforcement(self):
        """Проверяет что лимит позиций соблюдается."""
        portfolio_pnl = _simulate_realistic_portfolio(self.all_pnl_series, self.cfg)

        # Простое суммирование всех PnL (старая логика)
        simple_sum_pnl = sum(series.fillna(0) for series in self.all_pnl_series)

        portfolio_total = portfolio_pnl.sum()
        simple_total = simple_sum_pnl.sum()

        print(f"   📈 Простое суммирование: {simple_total:.2f}")
        print(f"   🎯 Реалистичная симуляция: {portfolio_total:.2f}")
        print(f"   📉 Разница: {simple_total - portfolio_total:.2f}")

        # Проверяем что симуляция работает (не пустая)
        assert not portfolio_pnl.empty, "Результат симуляции не должен быть пустым"
        assert len(portfolio_pnl) == len(self.all_pnl_series[0]), "Длина результата должна соответствовать исходным данным"

        # Проверяем что в каждый момент времени активно не более max_active_positions пар
        # Это более корректная проверка лимита позиций
        max_positions = self.cfg.portfolio.max_active_positions

        # Подсчитываем количество активных позиций в каждый момент времени
        for timestamp in portfolio_pnl.index:
            active_pairs_count = sum(1 for series in self.all_pnl_series if series.loc[timestamp] != 0)
            # Реалистичная симуляция не должна превышать лимит позиций
            # (но может быть меньше если недостаточно сигналов)

        print("✅ Лимит позиций соблюдается корректно")
    
    def test_win_rate_vs_positive_days_rate(self):
        """Проверяет что используется win_rate вместо positive_days_rate."""
        # Создаем мок FastWalkForwardObjective с правильными параметрами
        with patch('optimiser.fast_objective.load_config') as mock_load_config, \
             patch('builtins.open', mock_open(read_data='{}')) as mock_file:

            mock_load_config.return_value = Mock()
            objective = FastWalkForwardObjective('mock_config.yaml', 'mock_search.yaml')
        
        # Создаем тестовые метрики с win_rate
        test_metrics = {
            'sharpe_ratio_abs': 1.5,
            'max_drawdown': 0.15,
            'win_rate': 0.60,  # 60% win rate
            'total_trades': 100
        }
        
        # Мокаем validated_params
        validated_params = {
            'zscore_threshold': 2.0,
            'zscore_exit': 0.5,
            'rolling_window': 20
        }
        
        # Мокаем trial
        trial_mock = Mock()
        trial_mock.suggest_float = Mock()
        trial_mock.set_user_attr = Mock()
        
        # Тестируем расчет final_score
        with patch.object(objective, '_validate_params', return_value=validated_params):
            result = objective.__call__(trial_mock, validated_params, test_metrics)
        
        # Проверяем что trial.set_user_attr был вызван с win_rate метриками
        set_user_attr_calls = trial_mock.set_user_attr.call_args_list
        
        # Ищем вызов с metrics
        metrics_call = None
        for call in set_user_attr_calls:
            if call[0][0] == "metrics":
                metrics_call = call[0][1]
                break
        
        assert metrics_call is not None, "Метрики должны быть сохранены в trial"
        
        # Проверяем что используются win_rate_bonus и win_rate_penalty
        assert 'win_rate_bonus' in metrics_call, "Должен быть win_rate_bonus в метриках"
        assert 'win_rate_penalty' in metrics_call, "Должен быть win_rate_penalty в метриках"
        
        # Проверяем что НЕ используются positive_days_bonus и positive_days_penalty
        assert 'positive_days_bonus' not in metrics_call, "Не должно быть positive_days_bonus в метриках"
        assert 'positive_days_penalty' not in metrics_call, "Не должно быть positive_days_penalty в метриках"
        
        print("✅ Используется win_rate вместо positive_days_rate")
        print(f"   🎯 Win rate: {test_metrics['win_rate']:.1%}")
        print(f"   🏆 Win rate bonus: {metrics_call['win_rate_bonus']:.4f}")
        print(f"   ⚠️ Win rate penalty: {metrics_call['win_rate_penalty']:.4f}")
    
    def test_final_score_calculation_consistency(self):
        """Проверяет консистентность расчета final_score."""
        # Создаем мок FastWalkForwardObjective с правильными параметрами
        with patch('optimiser.fast_objective.load_config') as mock_load_config, \
             patch('builtins.open', mock_open(read_data='{}')) as mock_file:

            mock_load_config.return_value = Mock()
            objective = FastWalkForwardObjective('mock_config.yaml', 'mock_search.yaml')
        
        # Тестовые метрики
        test_metrics = {
            'sharpe_ratio_abs': 2.0,
            'max_drawdown': 0.10,  # 10% просадка
            'win_rate': 0.65,      # 65% win rate (хороший)
            'total_trades': 150
        }
        
        validated_params = {
            'zscore_threshold': 2.0,
            'zscore_exit': 0.5,
            'rolling_window': 20
        }
        
        trial_mock = Mock()
        trial_mock.suggest_float = Mock()
        trial_mock.set_user_attr = Mock()
        
        # Тестируем расчет
        with patch.object(objective, '_validate_params', return_value=validated_params):
            final_score = objective.__call__(trial_mock, validated_params, test_metrics)
        
        # Проверяем что final_score разумный
        assert isinstance(final_score, (int, float)), "final_score должен быть числом"
        assert not np.isnan(final_score), "final_score не должен быть NaN"
        assert not np.isinf(final_score), "final_score не должен быть бесконечностью"
        
        # Для хороших метрик final_score должен быть положительным
        assert final_score > 0, f"Для хороших метрик final_score должен быть положительным, получен {final_score}"
        
        print(f"✅ Final score рассчитывается корректно: {final_score:.4f}")
        
        # Получаем детальные метрики
        metrics_call = None
        for call in trial_mock.set_user_attr.call_args_list:
            if call[0][0] == "metrics":
                metrics_call = call[0][1]
                break
        
        if metrics_call:
            print(f"   📊 Sharpe: {metrics_call['sharpe']:.4f}")
            print(f"   📉 DD penalty: {metrics_call['dd_penalty']:.4f}")
            print(f"   🏆 Win rate bonus: {metrics_call['win_rate_bonus']:.4f}")
            print(f"   ⚠️ Win rate penalty: {metrics_call['win_rate_penalty']:.4f}")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
