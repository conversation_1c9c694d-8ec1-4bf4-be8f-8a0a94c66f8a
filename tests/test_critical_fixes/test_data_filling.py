#!/usr/bin/env python3
"""
Тест для проверки исправления lookahead bias при заполнении данных.

Проверяет что:
1. Нет использования bfill() или fillna(method='bfill')
2. Пропуски заполняются только ffill() или interpolate()
3. Метод _fill_gaps_session_aware используется повсеместно
"""

import pytest
import pandas as pd
import numpy as np
import sys
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Добавляем корневую директорию проекта в PYTHONPATH
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

from coint2.core.normalization_improvements import _fill_gaps_session_aware


@pytest.mark.unit
@pytest.mark.fast
class TestDataFilling:
    """Тесты для проверки корректного заполнения данных без lookahead bias."""
    
    @pytest.mark.unit
    def test_no_backward_fill_in_data_processing(self):
        """
        ТЕСТ 1: Проверяет что в обработке данных не используется bfill().

        Создает DataFrame с пропуском в начале торгового дня и проверяет
        что пропуск не заполняется значением с предыдущего дня.
        """
        # Создаем данные с пропуском в начале торгового дня
        dates_day1 = pd.date_range('2024-01-01 00:00:00', '2024-01-01 23:45:00', freq='15min')
        dates_day2 = pd.date_range('2024-01-02 00:00:00', '2024-01-02 23:45:00', freq='15min')

        # Данные первого дня - заканчиваем высоким значением
        data_day1 = pd.DataFrame({
            'price': [100 + i * 0.1 for i in range(len(dates_day1))],
            'volume': [1000 + i * 10 for i in range(len(dates_day1))]
        }, index=dates_day1)

        # Данные второго дня - ПОЛНОСТЬЮ пропущены в начале (чтобы ffill не мог заполнить)
        data_day2 = pd.DataFrame({
            'price': [np.nan] * len(dates_day2),  # Все значения NaN
            'volume': [np.nan] * len(dates_day2)  # Все значения NaN
        }, index=dates_day2)

        # Объединяем данные
        test_data = pd.concat([data_day1, data_day2])

        # Проверяем что пропуски в начале второго дня НЕ заполняются значениями с первого дня
        filled_data = _fill_gaps_session_aware(test_data, method='ffill')

        # Получаем последнее значение первого дня
        last_day1_price = data_day1['price'].iloc[-1]

        # Проверяем что первые значения второго дня НЕ равны последнему значению первого дня
        day2_start = '2024-01-02 00:00:00'
        day2_end = '2024-01-02 02:30:00'  # Первые 10 баров второго дня
        day2_early_data = filled_data.loc[day2_start:day2_end]

        # Все эти значения должны остаться NaN (не заполнены значениями с предыдущего дня)
        assert day2_early_data['price'].isna().all(), \
            "Пропуски в начале торгового дня не должны заполняться значениями с предыдущего дня"
        assert day2_early_data['volume'].isna().all(), \
            "Пропуски в начале торгового дня не должны заполняться значениями с предыдущего дня"

        # Дополнительная проверка: убеждаемся что значения не равны последнему значению предыдущего дня
        for price in day2_early_data['price'].dropna():
            assert price != last_day1_price, \
                f"Значение {price} равно последнему значению предыдущего дня {last_day1_price}, что указывает на backward fill"

        print(f"✅ Пропуски в начале торгового дня не заполняются backward fill")
        print(f"✅ Данные остаются NaN в начале второго дня")
        print(f"✅ Последнее значение дня 1: {last_day1_price}, первые значения дня 2: все NaN")

    def test_bfill_method_not_called(self):
        """
        ТЕСТ 2: Проверяет что метод pd.DataFrame.bfill не вызывался.
        
        Мокает метод bfill и проверяет что он не используется.
        """
        # Создаем тестовые данные с пропусками
        dates = pd.date_range('2024-01-01', periods=20, freq='15min')
        test_df = pd.DataFrame({
            'price': [100, 101, np.nan, 103, 104, np.nan, np.nan, 107] + [100 + i for i in range(12)],
            'volume': [1000, 1010, np.nan, 1030, 1040, np.nan, np.nan, 1070] + [1000 + i * 10 for i in range(12)]
        }, index=dates)
        
        # Мокаем методы bfill для отслеживания вызовов
        with patch('pandas.DataFrame.bfill') as mock_bfill_df:
            with patch('pandas.Series.bfill') as mock_bfill_series:
                # Вызываем функцию заполнения
                filled_df = _fill_gaps_session_aware(test_df, method='ffill')
                
                # Проверяем что bfill не был вызван
                assert not mock_bfill_df.called, \
                    "Метод DataFrame.bfill не должен вызываться"
                assert not mock_bfill_series.called, \
                    "Метод Series.bfill не должен вызываться"
                
                print(f"✅ Методы bfill не вызывались")
                print(f"✅ Используются только forward fill методы")

    def test_forward_fill_and_interpolation_used(self):
        """
        ТЕСТ 3: Проверяет что используются только ffill() и interpolate().
        
        Проверяет что система использует только допустимые методы заполнения.
        """
        # Создаем тестовые данные с пропусками
        dates = pd.date_range('2024-01-01', periods=10, freq='15min')
        test_df = pd.DataFrame({
            'price': [100, 101, np.nan, 103, 104, np.nan, np.nan, 107, 108, 109],
            'volume': [1000, 1010, np.nan, 1030, 1040, np.nan, np.nan, 1070, 1080, 1090]
        }, index=dates)
        
        # Мокаем допустимые методы заполнения
        with patch('pandas.DataFrame.ffill') as mock_ffill_df:
            with patch('pandas.Series.ffill') as mock_ffill_series:
                with patch('pandas.DataFrame.interpolate') as mock_interpolate_df:
                    with patch('pandas.Series.interpolate') as mock_interpolate_series:
                        # Тестируем ffill метод
                        filled_ffill = _fill_gaps_session_aware(test_df, method='ffill')
                        
                        # Тестируем interpolate метод
                        filled_interpolate = _fill_gaps_session_aware(test_df, method='linear')
                        
                        # Проверяем что допустимые методы вызывались
                        assert mock_ffill_df.called or mock_ffill_series.called, \
                            "Метод ffill должен использоваться"
                        assert mock_interpolate_df.called or mock_interpolate_series.called, \
                            "Метод interpolate должен использоваться"
                        
                        print(f"✅ Используются только допустимые методы заполнения")
                        print(f"✅ ffill и interpolate работают корректно")

    def test_session_aware_gap_filling(self):
        """
        ТЕСТ 4: Проверяет что _fill_gaps_session_aware работает правильно.
        
        Проверяет что пропуски заполняются только в пределах торговой сессии.
        """
        # Создаем данные с несколькими торговыми сессиями
        session1_dates = pd.date_range('2024-01-01 09:00:00', '2024-01-01 17:00:00', freq='15min')
        session2_dates = pd.date_range('2024-01-02 09:00:00', '2024-01-02 17:00:00', freq='15min')
        
        # Данные первой сессии
        session1_data = pd.DataFrame({
            'price': [100 + i * 0.05 for i in range(len(session1_dates))],
        }, index=session1_dates)
        
        # Данные второй сессии с пропусками
        session2_prices = [200 + i * 0.05 for i in range(len(session2_dates))]
        # Делаем некоторые значения NaN
        session2_prices[5] = np.nan
        session2_prices[10] = np.nan
        session2_prices[15] = np.nan
        
        session2_data = pd.DataFrame({
            'price': session2_prices,
        }, index=session2_dates)
        
        # Объединяем данные
        combined_data = pd.concat([session1_data, session2_data])
        
        # Заполняем пропуски
        filled_data = _fill_gaps_session_aware(combined_data, method='linear')
        
        # Проверяем что пропуски заполнились правильно (не NaN)
        session2_filled = filled_data.loc['2024-01-02 09:00:00':'2024-01-02 17:00:00']
        assert not session2_filled['price'].isna().any(), \
            "Все пропуски в торговой сессии должны быть заполнены"
        
        # Проверяем что значения заполнены логично (между соседними значениями)
        # Пропуск на индексе 5 должен быть между значениями индексов 4 и 6
        expected_value_5 = (session2_prices[4] + session2_prices[6]) / 2
        actual_value_5 = filled_data.loc[session2_dates[5], 'price']
        assert abs(actual_value_5 - expected_value_5) < 0.1, \
            f"Пропуск должен заполниться интерполяцией: ожидалось {expected_value_5:.2f}, получено {actual_value_5:.2f}"
        
        print(f"✅ _fill_gaps_session_aware работает корректно")
        print(f"✅ Пропуски заполняются только в пределах торговой сессии")

    def test_integration_with_existing_data_loader(self):
        """
        ИНТЕГРАЦИОННЫЙ ТЕСТ: Проверяет совместимость с существующим data loader.
        
        Убеждается что исправления не ломают существующую функциональность.
        """
        # Создаем реалистичные данные как в существующих тестах
        dates = pd.date_range('2024-01-01', periods=100, freq='15min')
        prices = [100 + np.random.randn() * 0.1 for _ in range(100)]
        volumes = [1000 + np.random.randint(-100, 100) for _ in range(100)]
        
        # Добавляем несколько пропусков
        prices[10] = np.nan
        prices[25] = np.nan
        volumes[15] = np.nan
        volumes[30] = np.nan
        
        test_df = pd.DataFrame({
            'price': prices,
            'volume': volumes
        }, index=dates)
        
        # Тестируем разные методы заполнения
        methods = ['ffill', 'linear', 'quadratic']
        
        for method in methods:
            try:
                filled_df = _fill_gaps_session_aware(test_df, method=method)
                
                # Проверяем что данные заполнены
                assert not filled_df.isna().any().any(), \
                    f"Все пропуски должны быть заполнены методом {method}"
                
                print(f"✅ Метод заполнения '{method}' работает корректно")
                
            except Exception as e:
                # Некоторые методы могут не работать с маленькими датасетами
                print(f"⚠️  Метод '{method}' не применим к этим данным: {e}")
        


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
