# Анализ потери валютных пар при нормализации

## Проблема

В логах наблюдается значительная потеря валютных пар при нормализации данных:
```
Training данные: 14,497 строк × 136 символов
После нормализации: 36 символов
```

Это означает, что из 136 исходных валютных пар до этапа фильтрации доходит только 36 (потеря 73.5%).

## Анализ фильтрации пар

На основе анализа файла `filter_reasons_20250705_151804.csv` выявлены следующие основные причины фильтрации:

1. **p-value коинтеграции**: 43.5% пар (1629) отбрасываются из-за недостаточной статистической значимости коинтеграции
2. **Корреляция**: 39.9% пар (1492) отфильтровываются из-за неподходящей корреляции (слишком высокая или слишком низкая)
3. **KPSS тест**: 16.4% пар (615) не проходят тест стационарности спреда

## Причины потери при нормализации

Основные причины потери пар на этапе нормализации:

1. **Постоянные цены**: Символы с неизменной ценой (max = min) создают деление на ноль
2. **Пропущенные значения**: Символы с пропусками (NaN) отбрасываются
3. **Эффект мин-макс нормализации**: Символы с экстремальными выбросами могут искажать нормализацию

## Рекомендации по улучшению

### 1. Для проблемы потери при нормализации:

- **Предобработка данных**:
  ```python
  # Фильтрация символов с постоянной ценой до нормализации
  constant_price_symbols = [col for col in training_slice.columns 
                           if training_slice[col].max() == training_slice[col].min()]
  training_slice = training_slice.drop(columns=constant_price_symbols)
  
  # Замена min-max нормализации на z-score
  normalized_training = (training_slice - training_slice.mean()) / training_slice.std()
  ```

- **Улучшенная обработка пропусков**:
  ```python
  # Заполнение небольших пропусков перед нормализацией
  training_slice = training_slice.interpolate(method='linear', limit=3)
  ```

### 2. Для проблемы p-value (43.5%):

- Увеличить порог p-value с 0.03 до 0.05 или 0.1
- Рассмотреть альтернативные тесты коинтеграции
- Увеличить минимальное количество точек данных для теста

### 3. Для проблемы корреляции (39.9%):

- Расширить допустимый диапазон корреляции:
  ```yaml
  min_correlation: 0.25  # Было 0.30
  max_correlation: 0.97  # Было 0.95
  ```

- Использовать динамическую корреляцию вместо статической

### 4. Для проблемы KPSS теста (16.4%):

- Увеличить порог p-value для KPSS теста до 0.05 (сейчас 0.10)
- Добавить предварительную обработку спреда для улучшения стационарности

## Символы, наиболее часто отфильтровываемые

Топ-10 символов, которые чаще всего отфильтровываются:
- LTCUSDT
- GALAUSDT
- COMPUSDT
- DOTUSDC
- DOGEUSDT
- MASKUSDT
- ADAUSDT
- SANDBTC
- LUNCUSDT
- UMAUSDT

Рекомендуется провести отдельный анализ этих символов для выявления причин их частой фильтрации.

## Предлагаемые изменения в конфигурации

```yaml
pair_selection:
  coint_pvalue_threshold: 0.05       # Увеличено с 0.03
  min_correlation: 0.25              # Уменьшено с 0.30
  max_correlation: 0.97              # Увеличено с 0.95
  min_spread_std: 0.003              # Уменьшено с 0.005
  max_spread_std: 10.0               # Увеличено с 5.0
```

## Дополнительные модификации кода

1. Добавить альтернативные методы нормализации в `walk_forward_orchestrator.py`:
```python
# Вариант 1: Z-score нормализация
normalized_training = (training_slice - training_slice.mean()) / training_slice.std()

# Вариант 2: Логарифмические доходности
normalized_training = np.log(training_slice / training_slice.shift(1)).dropna()
```

2. Улучшить фильтрацию в `filters.py`:
```python
# Ослабить проверку KPSS
if p_kpss < 0.05:  # Было 0.10
    filter_reasons.append((s1, s2, 'kpss'))
    continue
```

Эти изменения должны значительно увеличить количество пар, проходящих нормализацию и фильтрацию, что потенциально улучшит результаты стратегии за счет более широкого выбора торговых возможностей.