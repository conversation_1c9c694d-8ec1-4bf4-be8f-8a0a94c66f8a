# Документация по API-загрузчику криптовалютных данных

## Общее описание

API-загрузчик предназначен для получения исторических данных о криптовалютных парах с Bybit API с последующим сохранением в оптимизированный формат Parquet. Загрузчик использует многопоточность для параллельной обработки данных и двухфазную схему сохранения для предотвращения повреждения файлов при параллельной записи.

## Ключевые особенности

1. **Загрузка данных исключительно через API Bybit**
   - Получение списка доступных пар непосредственно с биржи
   - Загрузка 15-минутных интервалов (klines) для каждой пары

2. **Оптимизация и защита данных**
   - Параллельная загрузка с использованием ThreadPoolExecutor
   - Двухфазная схема сохранения данных: сначала во временные файлы, затем объединение
   - Инкрементальное объединение временных файлов каждые 10 загруженных валют
   - Автоматическое удаление дубликатов по временным меткам

3. **Структура хранения данных**
   - Оптимизированная структура директорий: `year=YYYY/month=MM/data_part_MM.parquet`
   - Все валюты за определенный месяц хранятся в одном файле (построчно)

4. **Безопасность и надежность**
   - Обработка исключений на всех этапах загрузки
   - Блокировки файлов для безопасной записи (через модуль file_lock_manager)
   - Финальная проверка на дубликаты после завершения загрузки

## Требования

1. Python 3.8+
2. Необходимые пакеты:
   - pybit
   - pandas
   - pyarrow
   - numpy
   - tqdm
   - logging
   - filelock

## Структура проекта

```
coint4/
  ├── data_downloaded/            # Директория для загруженных данных
  │   └── year=YYYY/              # Год
  │       └── month=MM/           # Месяц
  │           └── data_part_MM.parquet  # Данные за месяц
  │
  ├── temporary useful files/
  │   ├── api_loader_downloaded.py     # Основной скрипт загрузки
  │   ├── file_lock_manager.py         # Модуль для блокировки файлов
  │   └── parquet_duplicates_checker.py # Модуль проверки дубликатов
  │
  └── temp_files/                  # Временные файлы (создаются автоматически)
      └── [symbol]_[year]_[month].parquet
```

## Параметры командной строки

```
python api_loader_downloaded.py [параметры]
```

Основные параметры:
- `--start-date` - Начальная дата загрузки в формате YYYY-MM-DD (по умолчанию: 2022-01-01)
- `--end-date` - Конечная дата загрузки в формате YYYY-MM-DD (по умолчанию: 2025-07-01)
- `--incremental` - Режим дозагрузки только недостающих данных (15-минутные интервалы)
- `--symbols-limit` - Ограничение количества символов для загрузки (опционально)
- `--api-key` - API ключ Bybit (опционально, но рекомендуется для избежания ограничений)
- `--api-secret` - API секрет Bybit (опционально, но рекомендуется для избежания ограничений)

## Примеры использования

### Базовое использование:

```bash
python api_loader_downloaded.py
```

### Загрузка за определенный период:

```bash
python api_loader_downloaded.py --start-date 2023-01-01 --end-date 2023-12-31
```

### Загрузка с использованием API-ключей:

```bash
python api_loader_downloaded.py --api-key ВАШ_API_КЛЮЧ --api-secret ВАШ_API_СЕКРЕТ
```

### Загрузка ограниченного количества валют (для тестирования):

```bash
python api_loader_downloaded.py --symbols-limit 10
```

### Инкрементальная дозагрузка недостающих данных:

```bash
python api_loader_downloaded.py --incremental
```

### Комбинированное использование (дозагрузка с ограничением по времени и количеству валют):

```bash
python api_loader_downloaded.py --start-date 2023-01-01 --end-date 2023-01-31 --symbols-limit 20 --incremental
```

## Описание работы загрузчика

### Этап 1: Подготовка

1. Получение списка доступных торговых пар с Bybit API
2. Создание HTTP-сессий для параллельной загрузки данных
3. Создание директорий для сохранения данных

### Этап 2: Загрузка данных (Фаза 1)

1. Параллельная загрузка данных для каждой валютной пары
2. Сохранение данных во временные файлы с уникальным именем для каждой пары
3. Инкрементальное объединение временных файлов каждые 10 успешно загруженных валют

### Этап 3: Объединение данных (Фаза 2)

1. Объединение оставшихся временных файлов в итоговые месячные parquet-файлы
2. Удаление дубликатов по временным меткам
3. Финальная проверка всех файлов на наличие дубликатов

## Обработка ошибок

Загрузчик включает подробное логирование и обработку ошибок на всех этапах:
- Ошибки при получении данных с API
- Ошибки при обработке и фильтрации данных
- Ошибки при сохранении и объединении файлов

В случае возникновения ошибок для отдельных пар, загрузчик продолжает работу с остальными парами, и в конце предоставляет общую статистику по успешным и неуспешным загрузкам.

## Оптимизация производительности

1. **Параллельная загрузка**
   - Использование ThreadPoolExecutor для параллельной загрузки нескольких пар
   - Автоматическое определение оптимального количества потоков на основе CPU

2. **Двухфазная схема сохранения**
   - Предотвращает конфликты при параллельной записи
   - Минимизирует риск повреждения данных

3. **Инкрементальное объединение**
   - Снижает пиковую нагрузку на диск
   - Освобождает дисковое пространство во время загрузки

## Дополнительные рекомендации

1. Рекомендуется использовать API-ключи для увеличения лимитов запросов
2. Для очень больших диапазонов дат рекомендуется разбивать загрузку на несколько запусков
3. После загрузки рекомендуется проверить статистику и при необходимости повторить загрузку для пар с ошибками
4. Для экономии места на диске временные файлы автоматически удаляются после объединения

## Устранение неполадок

### Проблема: Ошибка соединения с API
**Решение:** Проверьте доступность API Bybit, убедитесь в правильности API-ключей или попробуйте уменьшить количество параллельных потоков.

### Проблема: Недостаточно места на диске
**Решение:** Используйте инкрементальное объединение файлов или уменьшите диапазон дат для загрузки.

### Проблема: Ошибки сохранения parquet-файлов
**Решение:** Система автоматически использует блокировки файлов и повторные попытки. Если проблема сохраняется, проверьте права доступа к директории.
